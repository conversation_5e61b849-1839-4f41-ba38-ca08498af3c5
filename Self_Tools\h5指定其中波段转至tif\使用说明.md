# H5 to TIFF Converter 使用说明

## 概述

这是一个用于将HDF5格式文件转换为GeoTIFF格式的Python脚本。该脚本支持批量转换、单文件转换、波段选择、数据归一化等多种功能。

## 功能特性

- **批量转换**：支持批量处理目录中的所有.h5文件
- **单文件转换**：支持转换单个指定的.h5文件
- **波段选择**：可以选择特定的波段进行转换，而不是转换所有波段
- **数据归一化**：提供多种数据归一化方法
- **地理坐标系统**：支持自定义坐标参考系统和边界框
- **进度显示**：使用进度条显示转换进度

## 依赖库

在使用脚本前，请确保安装以下Python库：

```bash
pip install h5py numpy rasterio tqdm
```

## 使用方法

### 1. 基本用法

#### 批量转换（默认）
```bash
python Image_transform.py
```
- 默认从 `test` 目录读取所有.h5文件
- 输出到 `output` 目录
- 不进行数据归一化
- 使用所有波段

#### 单文件转换
```bash
python Image_transform.py -f path/to/your/file.h5
```

### 2. 参数说明

| 参数 | 简写 | 默认值 | 说明 |
|------|------|--------|------|
| `--input-dir` | `-i` | `test` | 输入目录，包含.h5文件 |
| `--output-dir` | `-o` | `output` | 输出目录，保存.tiff文件 |
| `--normalization` | `-n` | `none` | 数据归一化方法 |
| `--single-file` | `-f` | 无 | 转换单个文件的路径 |
| `--crs` | `-c` | `EPSG:4326` | 坐标参考系统 |
| `--bounds` | `-b` | 无 | 边界框坐标 |
| `--slectindex` | `-s` | 无 | 选择的波段索引（从0开始） |

### 3. 数据归一化方法

- `none`：保持原始数据（默认）
- `minmax`：最小-最大归一化到0-255
- `percentile`：基于2%和98%分位数的归一化
- `zscore`：Z-score标准化

### 4. 波段选择示例

#### 选择特定波段
```bash
# 选择第1、2、3、7、10、11波段（索引从0开始）
python Image_transform.py -s 1 2 3 7 10 11

# 选择前3个波段
python Image_transform.py -s 0 1 2

# 选择单个波段
python Image_transform.py -s 5
```

### 5. 完整参数示例

```bash
# 完整参数示例
python Image_transform.py \
    --input-dir data/h5_files \
    --output-dir results/tiff_files \
    --normalization minmax \
    --crs EPSG:32633 \
    --bounds -180 -90 180 90 \
    --slectindex 0 1 2 3 4
```

```bash
# 转换单个文件并选择特定波段
python Image_transform.py \
    -f data/sample.h5 \
    -o results \
    -n percentile \
    -s 1 3 5 7
```

## 输出说明

### 文件命名规则
- 基本格式：`原文件名.tiff`
- 选择特定波段时：输出目录会添加波段信息后缀

### 输出信息
脚本运行时会显示：
- 找到的.h5文件数量
- 使用的归一化方法
- 坐标系统信息
- 波段选择信息
- 转换进度条
- 成功/失败转换统计

### 示例输出
```
Found 5 .h5 files to convert
Using normalization method: none
Using coordinate system: EPSG:4326
Output directory: output_bands_1_2_3_7_10_11
Band selection: Converting bands [1, 2, 3, 7, 10, 11]
Converting files: 100%|████████████| 5/5 [00:30<00:00,  6.12s/it]

Conversion completed!
Successful conversions: 5
Failed conversions: 0
```

## 注意事项

1. **数据结构要求**：脚本假设.h5文件中的图像数据存储在 `'img'` 键下
2. **波段索引**：波段索引从0开始计数
3. **内存使用**：处理大文件时注意内存使用情况
4. **坐标系统**：默认使用WGS84地理坐标系统（EPSG:4326）
5. **压缩格式**：输出的TIFF文件使用LZW压缩以减小文件大小

## 错误处理

脚本包含完善的错误处理机制：
- 检查文件是否存在
- 验证波段索引的有效性
- 处理不支持的数据格式
- 显示详细的错误信息

## 常见问题

### Q: 如何查看.h5文件包含多少个波段？
A: 可以使用Python查看：
```python
import h5py
with h5py.File('your_file.h5', 'r') as f:
    print(f"Data shape: {f['img'].shape}")
    # 如果是3D数据，第三个维度就是波段数
```

### Q: 转换后的文件很大怎么办？
A: 脚本已经使用了LZW压缩。如果仍然太大，可以考虑：
- 使用数据归一化（将float32转为uint8）
- 选择必要的波段而不是全部波段

### Q: 如何自定义坐标系统？
A: 使用 `--crs` 参数指定，例如：
```bash
python Image_transform.py --crs EPSG:32633  # UTM Zone 33N
```

## 技术支持

如果遇到问题，请检查：
1. 输入文件格式是否正确
2. 依赖库是否正确安装
3. 波段索引是否在有效范围内
4. 输出目录是否有写入权限
