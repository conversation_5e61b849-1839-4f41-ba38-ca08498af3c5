# 导入2.0版本掩码重建文件(inference中用到了，需要先导入)
from Prithvi_EO2_300MTL import prithvi_mae
# 导入2.0版本模型推理文件
from Prithvi_EO2_300MTL import inference_Separately_load as Prithvi_EO2
# 导入计算平均重建影像的函数
from Self_Tools.calculate_average_reconstruction import calculate_average_reconstruction
import os # 系统库
from tqdm import tqdm # 进度条
import json
import yaml
import numpy as np
import rasterio
import glob
import sys

# 定义一个遍历tif文件的程序 
def FindTIFF(Folder="0.Data"):
    # 获取文件信息列表
    file_info = []
    # 遍历待重建影像所在文件夹
    for root, dirs, files in os.walk(Folder):
        for file in files:
            file_path = os.path.join(root, file)
            file_info.append((file, file_path))
    # 返回文件列表及信息
    return file_info # file_info 是一个 [(文件名, 路径), ...] 的列表

# 定义一个重建图像函数，传入字典
def MAE(Input_Args,model):
    # 去原始图像文件后缀
    base_name, extension = os.path.splitext(Input_Args.get("file"))
    # 设置每张图导出路径
    out_folder_dir = os.path.join(Input_Args.get("out_folder_Rootdir"),
                                  base_name)
    # 于output文件夹内构建各个图像的文件夹，用于保存不同随机种子重建结果
    if not os.path.exists(out_folder_dir):
        os.makedirs(out_folder_dir)

    # 获取基础随机种子
    base_seed = Input_Args.get("seed")
    
    # 依次循环重建
    for i in tqdm(range(Input_Args.get("run_time"))):
        
        # 使用基础种子加上循环索引作为当前迭代的随机种子
        current_seed = base_seed + i if base_seed is not None else None
        Input_Args.update({"seed": current_seed})

        # 构建img和mask导出文件路径
        OriRGB_dir = os.path.join(out_folder_dir,f"origin_seed{current_seed}.tiff")
        img_dir = os.path.join(out_folder_dir,f"predicted_seed{current_seed}.tiff")
        mask_dir = os.path.join(out_folder_dir,f"mask_seed{current_seed}.tiff")
        
        # 构建一个OutPut_dict保存更新后文件保存路径
        OutPut_dict = {
            "OriRGB_dir": OriRGB_dir, # 若导出RGB时的输出路径
            "recon_img_dir": img_dir, # 重建影像输出路径
            "mask_img_dir": mask_dir, # 掩膜输出路径
        }
        
        # 更新输出目标文件夹
        Input_Args.update({"output_dir":out_folder_dir})
        # 获取输入模型的文件路径列表(用4张相同影像重建)
        data_file_list=[
            Input_Args.get("filedir"),
            # Input_Args.get("filedir"),
            # Input_Args.get("filedir"),
            # Input_Args.get("filedir"),
            ]
        # 输入图像传入模型
        Input_Args.update({"data_files":data_file_list})
        # 调用 main 函数，并将解析后的命令行参数作为关键字参数传递给它。
        Prithvi_EO2.Only_Inference(OutPut_dict=OutPut_dict,model=model,**Input_Args)
        # print(f"   第{i+1}次重建已完成")


if __name__ == "__main__":
    # 获取当前文件所在地址
    current_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(current_dir)  # 把当前工作目录切换到脚本所在目录
    print("当前文件所在目录是：", current_dir)

    # 设置配置参数
    MASK_RATIO = 0.25 # 掩码遮盖比例
    RUN_TIME = 32 # 每张图重建次数
    CONFIG = "config.json" # 配置文件文件名
    RGB_OUTPUTS = False # 是否输出RGB图像
    RECONSTRACT_OUTPUT_NAME = "Reconstract_Output" # 重建图像存储文件夹名称
    AVERAGE_RECONSTRACT_OUTPUT_NAME = "1.AverageReconstract_Output" # 平均重建影像存储文件夹名称
    MODELNAME = "Prithvi_EO_V2_300M_TL.pt" # .pt模型名
    MAE_PROJECT_NAME = 'Prithvi_EO2_300MTL' # 使用重建模型的项目文件名
    DATA_FOLDER_NAME = '0.Data' # 存储用数据文件夹
    BASE_SEED = 42 # 基础随机种子，确保结果可重复性
    # 链接配置文件位置
    config_dir = os.path.join(current_dir, MAE_PROJECT_NAME,CONFIG)
    # 构建重建图像导出文件夹
    Reconstract_Output_dir = os.path.join(current_dir, RECONSTRACT_OUTPUT_NAME)
    # 构建平均重建图像导出文件夹
    Average_Reconstract_Output_dir = os.path.join(current_dir, AVERAGE_RECONSTRACT_OUTPUT_NAME)
    # 构建模型.pt文件路径
    modelpath = os.path.join(current_dir,MAE_PROJECT_NAME, MODELNAME)
    # 如果目标文件夹不在，则创建新文件夹
    if not os.path.exists(Reconstract_Output_dir):
        os.makedirs(Reconstract_Output_dir)

    # 配置传入重建函数的字典
    Input_Args={
        "run_time":RUN_TIME, # 预设的重建次数
        "seed":BASE_SEED, # 重建所用随机种子（基础种子）
        "out_folder_Rootdir":Reconstract_Output_dir, # 输出文件所在根目录
        "file":None, # 输入图像的图像名，于循环中更新
        "filedir":None, # 输入图像的图像路径，于循环中更新

        # Prithvi_EO2推理所需常规参数
        "data_files":[], # 1次性传入模型图像列表（可以试试是否支持多个地点图像传入？）
        "config_path": config_dir, # "配置文件"路径
        "checkpoint": modelpath, # 传入模型路径
        "output_dir": None, # 于循环中更新
        "rgb_outputs": RGB_OUTPUTS, # 是否为RGB输出
        "mask_ratio": MASK_RATIO, # 掩码比例
        # "input_indices": [1, 2, 3, 7, 10, 11], # 输入波段
    } 
        
    # 遍历待重建影像数据文件夹
    file_info = FindTIFF(DATA_FOLDER_NAME) # 遍历待重建影像文件
    Img_number = len(file_info) # 获取待处理影像文件数量
    # 设置进度条
    progress_bar = tqdm(total=Img_number, desc="计算进度", unit="步", ncols=100)
    # 载入配置文件
    with open(config_dir, "r") as f:
        config = yaml.safe_load(f)['pretrained_cfg'] # 从配置文件加载预训练模型配置
    
    # 加载一个样例数据
    ex_file = file_info[0][1] # 先获得第0个文件，再获取其filepath路径
    # 加载输入数据，包括图像数据、时间坐标、地理位置坐标和元数据
    coords_encoding = config['coords_encoding'] # 获取编码配置
    num_frames = len([ex_file]) # 获取时间步数（文件数量）
    ex_input_data, ex_temporal_coords, ex_location_coords, ex_meta_data = Prithvi_EO2.load_example(
        file_paths=[ex_file], indices=Input_Args.get("input_indices"),
        mean=config['mean'], std=config['std'],
    )

    if len(ex_temporal_coords) != num_frames and 'time' in coords_encoding:
        coords_encoding.remove('time') # 如果时间坐标数量与时间步数不匹配，并且配置中包含时间编码，或者不存在时间，则移除时间编码
    if not len(ex_location_coords) and 'location' in coords_encoding:
        coords_encoding.remove('location') # 如果地理位置坐标为空，并且配置中包含位置编码，则移除位置编码
    
    config.update(
        coords_encoding=coords_encoding,
        num_frames=num_frames,
        in_chans=len(config['bands']),
    ) # 更新模型配置，包括坐标编码、时间步数和输入通道数

    # 加载模型
    model = Prithvi_EO2.load_model(config = config,
                                   checkpoint = modelpath)
    # 遍历待处理影像文件夹内的每张图，并对每张图重建RUN_TIME次
    for idx, f in enumerate(file_info):
        # 解图像路径及图片信息
        file, filedir = f[0],f[1]
        # 更新配置参数
        Input_Args.update({"file": file}) # 更新正在处理图像名
        Input_Args.update({"filedir": filedir}) # 更新正在处理图像路径
        # 重新刷新随机种子至base状态
        Input_Args.update({"seed": BASE_SEED})
        # 执行掩码重建的推理任务
        MAE(Input_Args,model)
        # print(f"第{idx+1}张图已完成重建") # 打印进度提示，并更新进度条
        progress_bar.update(1)
    
    # 计算所有重建影像的平均值
    calculate_average_reconstruction(Reconstract_Output_dir, Average_Reconstract_Output_dir)