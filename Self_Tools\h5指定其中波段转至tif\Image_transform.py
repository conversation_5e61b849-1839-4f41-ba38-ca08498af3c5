#!/usr/bin/env python3
"""
H5 到 TIFF 转换器

此脚本将 'test' 文件夹中的 .h5 文件转换为 .tiff 格式。
每个 .h5 文件包含图像数据，将被转换为多波段 TIFF 文件。
"""

import os
import glob
import h5py
import numpy as np
import rasterio
from rasterio.transform import from_bounds
from rasterio.crs import CRS
from tqdm import tqdm


def normalize_data(data, method='none'):
    """
    数据归一化处理。

    Args:
        data: 待归一化的 numpy 数组
        method: 归一化方法 ('minmax', 'percentile', 'zscore', 'none')

    Returns:
        归一化后的数据 - 归一化方法返回 uint8，'none' 返回 float32
    """
    if method == 'minmax':
        # 最小-最大归一化
        data_min = np.min(data)
        data_max = np.max(data)
        if data_max > data_min:
            normalized = (data - data_min) / (data_max - data_min) * 255
        else:
            normalized = np.zeros_like(data)
        return normalized.astype(np.uint8)
    elif method == 'percentile':
        # 百分位数归一化 (2% 和 98% 百分位数)
        p2, p98 = np.percentile(data, (2, 98))
        normalized = np.clip((data - p2) / (p98 - p2) * 255, 0, 255)
        return normalized.astype(np.uint8)
    elif method == 'zscore':
        # Z-score 归一化
        mean = np.mean(data)
        std = np.std(data)
        if std > 0:
            normalized = np.clip((data - mean) / std * 50 + 127.5, 0, 255)
        else:
            normalized = np.full_like(data, 127.5)
        return normalized.astype(np.uint8)
    elif method == 'none':
        # 保持原始数据类型和精度
        return data
        # return data.astype(np.float32)
    else:
        raise ValueError(f"Unknown normalization method: {method}")


def convert_h5_to_geotiff(h5_path, output_dir='output', normalization='none',
                         crs='EPSG:4326', bounds=None, slectindex=None):
    """
    使用 rasterio 将单个 .h5 文件转换为 GeoTIFF 格式。

    Args:
        h5_path: 输入 .h5 文件路径
        output_dir: 输出 .tiff 文件保存目录
        normalization: 数据归一化方法
        crs: 坐标参考系统 (默认: EPSG:4326)
        bounds: 边界框 (left, bottom, right, top) 或 None
        slectindex: 要选择的波段索引列表 (从0开始)，None 表示所有波段

    Returns:
        创建的 .tiff 文件路径，如果失败则返回 None
    """
    try:
        # 读取 h5 文件
        with h5py.File(h5_path, 'r') as f:
            # 假设数据存储在 'img' 键下
            if 'img' not in f:
                print(f"Warning: 'img' key not found in {h5_path}. Available keys: {list(f.keys())}")
                return None
            
            data = f['img'][...]
        
        # 获取不带扩展名的文件名
        base_name = os.path.splitext(os.path.basename(h5_path))[0]

        # 如果选择了特定波段，将波段信息添加到文件名中
        if slectindex is not None and len(data.shape) == 3:
            band_suffix = f"_bands_{'_'.join(map(str, slectindex))}"
            output_dir = output_dir + band_suffix # 更新导出的文件夹所处名称
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, f"{base_name}.tiff")
        
        # 处理不同数据形状
        if len(data.shape) == 3:
            # 多波段图像 (高度, 宽度, 通道数)
            height, width, total_channels = data.shape

            # 处理波段选择
            if slectindex is None:
                # 如果没有指定波段索引，使用所有波段
                selected_bands = list(range(total_channels))
                print(f"No band indices specified, using all {total_channels} bands")
            else:
                # 验证波段索引的有效性
                selected_bands = []
                for idx in slectindex:
                    if 0 <= idx < total_channels:
                        selected_bands.append(idx)
                    else:
                        print(f"Warning: Band index {idx} is out of range (0-{total_channels-1}), skipping")

                if not selected_bands:
                    print(f"Error: No valid band indices provided for {h5_path}")
                    return None

                print(f"Selected {len(selected_bands)} bands: {selected_bands}")

            channels = len(selected_bands)

            # 单独归一化每个选定通道并确定输出数据类型
            first_band_idx = selected_bands[0]
            first_channel_normalized = normalize_data(data[:, :, first_band_idx], method=normalization)
            output_dtype = first_channel_normalized.dtype

            # 创建包含正确数据类型和仅选定波段的输出数组
            normalized_data = np.zeros((height, width, channels), dtype=output_dtype)
            normalized_data[:, :, 0] = first_channel_normalized

            for i, band_idx in enumerate(selected_bands[1:], 1):
                normalized_data[:, :, i] = normalize_data(data[:, :, band_idx], method=normalization)

            # 调整数据维度为 (通道数, 高度, 宽度) 以适应 rasterio
            normalized_data = np.transpose(normalized_data, (2, 0, 1))

            # 设置坐标系和变换
            if bounds is None:
                # 默认边界 (可根据数据自定义)
                bounds = (0, 0, width, height)

            transform = from_bounds(bounds[0], bounds[1], bounds[2], bounds[3], width, height)

            # 使用 rasterio 保存为多波段 GeoTIFF
            with rasterio.open(
                output_path,
                'w',
                driver='GTiff',
                height=height,
                width=width,
                count=channels,  # 这里的channels已经是选择后的波段数
                dtype=output_dtype,
                crs=crs,
                transform=transform,
                compress='lzw'  # 添加压缩以减小文件大小
            ) as dst:
                # 写入每个选定波段
                for i in range(channels):
                    dst.write(normalized_data[i], i + 1)

                # 添加波段描述信息到metadata
                if slectindex is not None:
                    band_descriptions = [f"Original_Band_{selected_bands[i]}" for i in range(channels)]
                    dst.descriptions = band_descriptions
                    print(f"Saved {channels} selected bands to {output_path}")
                else:
                    print(f"Saved all {channels} bands to {output_path}")
            
        elif len(data.shape) == 2:
            # 单波段图像 (高度, 宽度)
            height, width = data.shape
            normalized_data = normalize_data(data, method=normalization)

            # 设置坐标系和变换
            if bounds is None:
                bounds = (0, 0, width, height)

            transform = from_bounds(bounds[0], bounds[1], bounds[2], bounds[3], width, height)

            # 使用 rasterio 保存为单波段 GeoTIFF
            with rasterio.open(
                output_path,
                'w',
                driver='GTiff',
                height=height,
                width=width,
                count=1,
                dtype=normalized_data.dtype,
                crs=crs,
                transform=transform,
                compress='lzw'
            ) as dst:
                dst.write(normalized_data, 1)
            
        else:
            print(f"Warning: Unsupported data shape {data.shape} in {h5_path}")
            return None
        
        return output_path
        
    except Exception as e:
        print(f"Error converting {h5_path}: {str(e)}")
        return None


def batch_convert_h5_to_geotiff(input_dir='test', output_dir='output', normalization='none',
                               crs='EPSG:4326', bounds=None, slectindex=None):
    """
    将输入目录中的所有 .h5 文件转换为 GeoTIFF 格式。

    Args:
        input_dir: 包含 .h5 文件的目录
        output_dir: .tiff 文件保存目录
        normalization: 归一化方法 ('minmax', 'percentile', 'zscore', 'none')
        crs: 坐标参考系统 (默认: EPSG:4326)
        bounds: 边界框 (left, bottom, right, top) 或 None
        slectindex: 要选择的波段索引列表 (从0开始)，None 表示所有波段
    """
    # 查找输入目录中所有 .h5 文件
    h5_files = glob.glob(os.path.join(input_dir, '*.h5'))

    if not h5_files:
        print(f"No .h5 files found in {input_dir}")
        return

    print(f"Found {len(h5_files)} .h5 files to convert")
    print(f"Using normalization method: {normalization}")
    print(f"Using coordinate system: {crs}")
    print(f"Output directory: {output_dir}")

    if slectindex is None:
        print("Band selection: All bands will be converted")
    else:
        print(f"Band selection: Converting bands {slectindex}")

    successful_conversions = 0
    failed_conversions = 0

     # 使用进度条转换每个文件
    for h5_file in tqdm(h5_files, desc="Converting files"):
        result = convert_h5_to_geotiff(h5_file, output_dir, normalization, crs, bounds, slectindex)
        if result:
            successful_conversions += 1
        else:
            failed_conversions += 1

    print(f"\nConversion completed!")
    print(f"Successful conversions: {successful_conversions}")
    print(f"Failed conversions: {failed_conversions}")


def main():

    import argparse

    parser = argparse.ArgumentParser(description='Convert .h5 files to GeoTIFF format')
    parser.add_argument('--input-dir', '-i', default='test',
                       help='Input directory containing .h5 files (default: test)')
    parser.add_argument('--output-dir', '-o', default='output',
                       help='Output directory for .tiff files (default: output)')
    parser.add_argument('--normalization', '-n', default='none',
                       choices=['minmax', 'percentile', 'zscore', 'none'],
                       help='Normalization method (default: none)')
    parser.add_argument('--single-file', '-f',
                       help='Convert a single .h5 file instead of batch processing')
    parser.add_argument('--crs', '-c', default='EPSG:4326',
                       help='Coordinate reference system (default: EPSG:4326)')
    parser.add_argument('--bounds', '-b', nargs=4, type=float, metavar=('LEFT', 'BOTTOM', 'RIGHT', 'TOP'),
                       help='Bounding box coordinates: left bottom right top')
    parser.add_argument('--slectindex', '-s', nargs='*', type=int, default=None,
                       help='Selected band indices (0-based). If not specified, all bands will be converted. Example: -s 0 1 2 for first 3 bands')

    args = parser.parse_args()

    # 如果提供了边界，将其转换为元组
    bounds = tuple(args.bounds) if args.bounds else None

    if args.single_file:
         # 转换单个文件
        if not os.path.exists(args.single_file):
            print(f"Error: File {args.single_file} does not exist")
            return

        result = convert_h5_to_geotiff(args.single_file, args.output_dir, 
                                       args.normalization, args.crs, 
                                       bounds, args.slectindex)
        if result:
            print(f"Successfully converted {args.single_file} to {result}")
        else:
            print(f"Failed to convert {args.single_file}")
    else:
        # 批量转换
        batch_convert_h5_to_geotiff(args.input_dir, args.output_dir, 
                                    args.normalization, args.crs, 
                                    bounds, args.slectindex)


if __name__ == "__main__":
    main()
    # 需指定波段使用时的用法
    # python Image_transform.py -s 1 2 3 7 10 11