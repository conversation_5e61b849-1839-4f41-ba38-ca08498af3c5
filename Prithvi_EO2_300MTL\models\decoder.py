
"""
MAE解码器模块
MAE decoder module for Prithvi model
"""

from typing import List, Tuple
import torch
import torch.nn as nn
from timm.models.vision_transformer import Block

from .coordinate_encoders import TemporalEncoder, LocationEncoder
from ..utils.position_encoding import get_3d_sincos_pos_embed
from ..utils.model_utils import _init_weights

# 模型解码器
class MAEDecoder(nn.Module):
    """ Transformer Decoder used in the Prithvi MAE
    Prithvi MAE中使用的Transformer解码器，用于重建被掩码的图像区域
    """
    def __init__(self,
                 patch_size: int | Tuple[int, int, int] = (1, 16, 16), # 原始 Patch 的尺寸 (T_p, H_p, W_p)
                 grid_size: List[int] | Tuple[int, int, int] = (3, 14, 14), # 原始图像的 Patch 网格尺寸 (T_grid, H_grid, W_grid)
                 in_chans: int = 3, # 原始输入图像的通道数（例如 RGB 为 3，多光谱为更多）
                 encoder_embed_dim: int = 1024, # 编码器输出的特征维度
                 decoder_embed_dim: int = 512, # 解码器内部的特征维度，通常小于编码器维度以降低计算成本
                 depth: int = 8, # 解码器 Transformer 块的数量
                 num_heads: int = 16, # 解码器 Transformer 块中的注意力头数
                 mlp_ratio: float = 4., # MLP 层的隐藏层维度与解码器嵌入维度之比
                 norm_layer: nn.Module = nn.LayerNorm, # 归一化层类型
                 coords_encoding: List[str] | None = None, # 列表，指定是否包含 'time' 和/或 'location' 编码 (与编码器保持一致)
                 coords_scale_learn: bool = False,  # 坐标编码的缩放因子是否可学习 (与编码器保持一致)
                 ):
        super().__init__() # 调用父类的初始化方法
        # 将编码器输出的特征维度（encoder_embed_dim）投影到解码器所需的维度（decoder_embed_dim）
        self.decoder_embed = nn.Linear(encoder_embed_dim, decoder_embed_dim, bias=True)
        self.decoder_embed_dim = decoder_embed_dim # 解码器嵌入维度传入
        self.grid_size = grid_size # 整个图像的 Patch 网格尺寸传入
        if isinstance(patch_size, int): # 如果 patch_size 是整数，将其转换为元组
            patch_size = (1, patch_size, patch_size)
        self.patch_size = patch_size # 单个 Patch 的实际像素尺寸
        # 计算总帧数 (用于时间编码，通过 Patch 网格的时间维度和 Patch 自身的时间维度计算)
        self.num_frames = self.grid_size[0] * patch_size[0]
        # 计算总的 Patch 数量
        num_patches = self.grid_size[0] * self.grid_size[1] * self.grid_size[2]

        # 可选的时间和位置编码（与编码器类似，用于为被重建的 Patch 提供上下文）
        coords_encoding = coords_encoding or []
        self.temporal_encoding = 'time' in coords_encoding
        self.location_encoding = 'location' in coords_encoding
        if self.temporal_encoding: 
            # 解码器的时间编码器，使用 decoder_embed_dim
            self.temporal_embed_dec = TemporalEncoder(decoder_embed_dim, coords_scale_learn)
        if self.location_encoding: 
            # 解码器的位置编码器，使用 decoder_embed_dim
            self.location_embed_dec = LocationEncoder(decoder_embed_dim, coords_scale_learn)
        
        # Mask Token：一个可学习的特殊 Token，用于表示被掩码的 Patch
        self.mask_token = nn.Parameter(torch.zeros(1, 1, decoder_embed_dim))
        
        # 解码器的位置编码：为所有 Patch (包括可见和掩码的) 和 CLS Token 提供位置信息 并注册为缓冲区
        self.register_buffer("decoder_pos_embed", torch.zeros(1, num_patches + 1, decoder_embed_dim))
        
        # 解码器 Transformer 块，在内部通过循环创建
        self.decoder_blocks = nn.ModuleList(
            [Block(decoder_embed_dim, num_heads, mlp_ratio, qkv_bias=True, norm_layer=norm_layer) for _ in range(depth)]
        )

        self.decoder_norm = norm_layer(decoder_embed_dim) # 解码器最终的 LayerNorm
        # Patch 预测器：将解码器 Transformer 的输出投影回原始 Patch 的像素空间
        # 输出维度是 `Patch 的像素数量 * 输入通道数`
        self.decoder_pred = nn.Linear(decoder_embed_dim,
                                      patch_size[0] * patch_size[1] * patch_size[2] * in_chans,
                                      bias=True)
        self.initialize_weights() # 调用权重初始化函数

    def initialize_weights(self):
        """
        初始化解码器的权重，包括位置编码、Mask Token 和 Transformer 块。
        """
        # initialize (and freeze) position embeddings by sin-cos embedding
        # 初始化解码器的位置编码（3D 正弦余弦编码）
        decoder_pos_embed = get_3d_sincos_pos_embed(
            self.decoder_pos_embed.shape[-1], self.grid_size, add_cls_token=True
        )
        self.decoder_pos_embed.data.copy_(torch.from_numpy(decoder_pos_embed).float().unsqueeze(0))

        # timm's trunc_normal_(std=.02) is effectively normal_(std=0.02) as cutoff is too big (2.)
        torch.nn.init.normal_(self.mask_token, std=0.02) # 初始化 Mask Token
        self.apply(_init_weights) # 对所有子模块应用通用的权重初始化函数 `_init_weights`

    def forward(
        self,
        hidden_states: torch.Tensor, # 编码器输出的可见 Patch 嵌入和 CLS Token (B, num_visible_patches + 1, encoder_embed_dim)
        ids_restore: torch.Tensor, # 编码器 `random_masking` 返回的用于恢复原始顺序的索引 (B, total_num_patches)
        temporal_coords: None | torch.Tensor = None,  # 原始输入的时间坐标 (B, T, 2)
        location_coords: None | torch.Tensor = None,  # 原始输入的位置坐标 (B, 2)
        input_size: list[int] = None, # 原始输入数据的时空尺寸 (T, H, W)
    ):
        """
        前向传播函数，用于根据编码器的输出和掩码信息，重建被掩码的图像区域。
        Returns:
            torch.Tensor: 重建后的 Patch 序列的像素值，形状为 (batch_size, total_num_patches, patch_pixel_dim)。
                          其中 `patch_pixel_dim = patch_size[0] * patch_size[1] * patch_size[2] * in_chans`。
        """
        # 1. 编码器输出维度投影到解码器维度
        x = self.decoder_embed(hidden_states)

        t, h, w = input_size[-3:] # 提取输入数据的时空尺寸 (T, H, W)
        # 2. 添加位置编码
        decoder_pos_embed = torch.from_numpy(
            get_3d_sincos_pos_embed(
                self.decoder_embed_dim,
                (
                    t // self.patch_size[0],
                    h // self.patch_size[1],
                    w // self.patch_size[2],
                ),
                add_cls_token=True,
            )
        ).to(x)

        # 3.填充mask token 并恢复原始patch顺序
        mask_tokens = self.mask_token.repeat(x.shape[0], ids_restore.shape[1] + 1 - x.shape[1], 1)
        x_ = torch.cat([x[:, 1:, :], mask_tokens], dim=1)  # 将可见 Patch 和 Mask Token 拼接起来 (暂时不含 CLS token)
        # 使用 ids_restore 恢复原始 Patch 顺序。
        x_ = torch.gather(x_, dim=1, index=ids_restore.unsqueeze(-1).repeat(1, 1, x.shape[2]).to(x_.device))
        x = torch.cat([x[:, :1, :], x_], dim=1)  # 将 CLS token 重新拼接到序列开头
        # 4. 添加位置编码
        x = x + decoder_pos_embed

        # 5. 再次分离 CLS token (为了在添加时间/位置编码时不对 CLS token 操作)
        x_ = x[:, 1:, :]

        if self.temporal_encoding: # 6. 添加时间编码 (如果启用)
            num_tokens_per_frame = x_.shape[1] // self.num_frames
            temporal_encoding = self.temporal_embed_dec(temporal_coords, num_tokens_per_frame)
            # Add temporal encoding w/o cls token
            x_ = x_ + temporal_encoding
        if self.location_encoding: # 7. 添加地理位置编码 (如果启用)
            location_encoding = self.location_embed_dec(location_coords)
            # Add location encoding w/o cls token
            x_ = x_ + location_encoding

        # 8. 重新将 CLS token 拼接到 Patch 序列
        x = torch.cat([x[:, :1, :], x_], dim=1)

        # 9. 通过解码器 Transformer 块
        for block in self.decoder_blocks:
            x = block(x)
        x = self.decoder_norm(x) # 最终的 LayerNorm
        
        # 10. Patch 预测器：将 Transformer 输出的特征投影回原始像素空间
        pred = self.decoder_pred(x) # 形状 (B, total_num_patches + 1, patch_pixel_dim)

        # 11. 移除 CLS token (通常只对 Patch 的预测感兴趣)
        pred = pred[:, 1:, :]

        return pred