
"""
ViT编码器模块
ViT encoder module for Prithvi model
"""

from functools import partial
from typing import List, Tuple
import numpy as np
import torch
import torch.nn as nn
from einops import rearrange
from timm.layers import to_2tuple
from timm.models.vision_transformer import Block

from .patch_embed import PatchEmbed
from .coordinate_encoders import TemporalEncoder, LocationEncoder
from ..utils.position_encoding import get_3d_sincos_pos_embed
from ..utils.model_utils import _init_weights

# ViT编码器
class PrithviViT(nn.Module):
    """ Prithvi ViT Encoder
    Prithvi ViT编码器，基于Vision Transformer架构，专为地球观测数据设计
    """
    def __init__(self,
                 img_size: int | Tuple[int, int] = 224,
                 patch_size: int | Tuple[int, int, int] = (1, 16, 16),
                 num_frames: int = 1, # 输入的时间帧数
                 in_chans: int = 3,
                 embed_dim: int = 1024, # Patch 嵌入和 Transformer 内部特征的维度
                 depth: int = 24, # Transformer 编码器块的数量 (深度)
                 num_heads: int = 16, # 注意力机制的头数
                 mlp_ratio: float = 4., # MLP 层的隐藏层维度与嵌入维度之比 (embed_dim * mlp_ratio)
                 norm_layer: nn.Module = partial(torch.nn.LayerNorm, eps=1e-6), # 归一化层类型，默认为 LayerNorm
                 coords_encoding: List[str] | None = None, # 列表，指定是否包含 'time' 和/或 'location' 编码
                 coords_scale_learn: bool = False, # 坐标编码的缩放因子是否可学习
                 encoder_only: bool = True,   # 是否只返回编码器输出 (用于与timm库兼容)
                 ** kwargs,
                ):
        super().__init__()

        self.feature_info = [] 
        self.encoder_only = encoder_only
        self.in_chans = in_chans
        self.num_frames = num_frames
        self.embed_dim = embed_dim
        self.img_size = to_2tuple(img_size) # 确保 img_size 是 (H, W) 元组
        # 处理 patch_size 的输入
        if isinstance(patch_size, int):
            patch_size = (1, patch_size, patch_size) # 如果只给了一个整数，默认为空间 Patch，时间维度为 1

        # 3D patch embedding
        self.patch_embed = PatchEmbed(
            input_size=(num_frames,) + self.img_size, # 组合成 (T, H, W)
            patch_size=patch_size,
            in_chans=in_chans,
            embed_dim=embed_dim,
        )

        # 可选的时间和位置编码模块
        coords_encoding = coords_encoding or []
        self.temporal_encoding = 'time' in coords_encoding # 检查是否启用时间编码
        self.location_encoding = 'location' in coords_encoding # 检查是否启用位置编码
        if self.temporal_encoding: 
            # 如果启用时间编码，要求 Patch 在时间维度上不能跨帧 (即 patch_size[0] 必须是 1)
            assert patch_size[0] == 1, f"With temporal encoding, patch_size[0] must be 1, received {patch_size[0]}"
            self.temporal_embed_enc = TemporalEncoder(embed_dim, coords_scale_learn)
        if self.location_encoding:
            self.location_embed_enc = LocationEncoder(embed_dim, coords_scale_learn)
        # 添加 CLS token 和位置嵌入（位置嵌入的形状是 (1, num_patches + 1, embed_dim)，+1 是为了容纳 cls_token）
        self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dim)) # 先生成全0张量
        self.register_buffer("pos_embed", torch.zeros(1, self.patch_embed.num_patches + 1, embed_dim)) 

        # Transformer 编码器层
        self.blocks = [] # 存储 Transformer 编码器块的列表（即模型结构列表）
        for i in range(depth):
            # 添加多个 Transformer Block。每个 Block 包含多头自注意力机制和 MLP。
            # embed_dim：嵌入向量的维度, num_heads：多头自注意力的头数, mlp_ratio：MLP 层的隐藏层维度与嵌入维度之比
            # qkv_bias：是否为 QKV 矩阵添加偏置项, norm_layer：归一化层类型
            self.blocks.append(Block(embed_dim, num_heads, mlp_ratio, qkv_bias=True, norm_layer=norm_layer))
            # 记录特征信息，用于多尺度特征提取
            self.feature_info.append(
                # num_chs: 计算当前块的通道数。reduction = 1 表示没有下采样。 module: 第i个模块
                {"num_chs": embed_dim * self.patch_embed.patch_size[0], "reduction": 1, "module": f"blocks.{i}"}
            )
        # 传入模型至模型文件
        self.blocks = nn.ModuleList(self.blocks)
        # 最终的 LayerNorm 层
        self.norm = norm_layer(embed_dim)
        # 调用权重初始化函数
        self.initialize_weights()

    def initialize_weights(self):
        """
        初始化模型的权重，包括位置编码、Patch 嵌入和 Transformer 块。
        """
        # 初始化（并冻结）位置嵌入，使用 3D 正弦余弦编码。
        pos_embed = get_3d_sincos_pos_embed(
            self.pos_embed.shape[-1], self.patch_embed.grid_size, add_cls_token=True
        )
        self.pos_embed.data.copy_(torch.from_numpy(pos_embed).float().unsqueeze(0))

        # 初始化 Patch Embedding 层的卷积权重，使其行为更像 nn.Linear (instead of nn.Conv2d)
        w = self.patch_embed.proj.weight.data # 访问patch_embed实例卷积的数据
        torch.nn.init.xavier_uniform_(w.view([w.shape[0], -1])) # Xavier 均匀分布初始化

        # timm 库中 `trunc_normal_(std=.02)` 实际上效果接近 `normal_(std=0.02)`
        torch.nn.init.normal_(self.cls_token, std=0.02)
        self.apply(_init_weights) # 对所有子模块应用通用的权重初始化函数 `_init_weights`

    def random_masking(self, sequence, mask_ratio, noise=None):
        """
        通过对每个样本进行随机打乱来执行随机掩码。每个样本的打乱是通过对随机噪声进行argsort实现的。

        Args:
            sequence (`torch.FloatTensor` of shape `(batch_size, sequence_length, dim)`)
                输入的token序列，形状为(batch_size, sequence_length, dim)
            mask_ratio (float): 使用的掩码比例
            noise (`torch.FloatTensor` of shape `(batch_size, sequence_length)`, *optional*) which is
                mainly used for testing purposes to control randomness and maintain the reproducibility
                Note: 当使用torch.manual_seed()设置随机种子时，这里生成的随机噪声也会受到影响，从而实现可重复的掩码生成
                可选的噪声张量，主要用于测试目的，控制随机性并保持可重现性
        Returns:
            tuple:
                - `sequence_unmasked`: 被保留的 (未掩码的) Patch 序列。
                - `mask`: 二进制掩码张量，0 表示保留，1 表示被掩码。
                - `ids_restore`: 用于恢复原始 Patch 顺序的索引。
        """
        batch_size, seq_length, dim = sequence.shape
        len_keep = int(seq_length * (1 - mask_ratio)) # 要保留的 Patch 数量

        if noise is None:
            noise = torch.rand(batch_size, seq_length, device=sequence.device) # 在 [0, 1] 之间生成随机噪声

        # 对每个样本的噪声进行排序，小的噪声值对应的 Patch 会被保留
        ids_shuffle = torch.argsort(noise, dim=1).to(sequence.device)  # 升序排列，使用`argsort`返回排序后的索引
        ids_restore = torch.argsort(ids_shuffle, dim=1).to(sequence.device) # 再次升序排列，使用`argsort`返回排序后的索引

        # 保留前 `len_keep` 个 Patch
        ids_keep = ids_shuffle[:, :len_keep]
        # 使用 `gather` 函数，用于从张量中按指定索引提取元素（根据 `ids_keep` 索引从原始序列中提取保留的 Patch）
        # `ids_keep 是保留（未被掩码）的token的索引. unsqueeze(-1) 在最后添加一个维度，将形状从 [batch_size, num_keep] 变为 [batch_size, num_keep, 1]
        # .repeat(1, 1, dim) - 将上一步得到的索引张量在最后一个维度上重复 dim 次
        sequence_unmasked = torch.gather(sequence, dim=1, index=ids_keep.unsqueeze(-1).repeat(1, 1, dim))

        # 生成二进制掩码：0 表示保留，1 表示移除（torch.ones代表创建一个全为1的掩码）
        mask = torch.ones([batch_size, seq_length], device=sequence.device)
        mask[:, :len_keep] = 0 # 将保留的 Patch 对应的掩码设为 0
        # 将掩码重新排列，使其与随机排序后的序列对应
        mask = torch.gather(mask, dim=1, index=ids_restore)

        return sequence_unmasked, mask, ids_restore

    def _get_pos_embed(self, x):
        """
        根据当前输入张量的尺寸动态生成位置编码。
        当输入的时空尺寸与模型初始化时预设的尺寸不同时，会调用此方法。
        """
        t, h, w = x.shape[-3:]  # 提取输入张量的时空尺寸 (T, H, W)
        
        # 根据当前输入尺寸的 Patch 网格大小生成 3D 正弦余弦位置编码
        pos_embed = torch.from_numpy(get_3d_sincos_pos_embed(
            self.embed_dim,
            (
                t // self.patch_embed.patch_size[0], # 计算时间维度上的 Patch 数量
                h // self.patch_embed.patch_size[1],
                w // self.patch_embed.patch_size[2],
            ),
            add_cls_token=True, # 也要为 cls_token 留出位置
        )).float().unsqueeze(0).to(x) # 转换为 Tensor，增加批次维度，并移动到与输入 x 相同的设备

        return pos_embed


    def forward(
        self, x: torch.Tensor,
        temporal_coords: None | torch.Tensor = None,
        location_coords: None | torch.Tensor = None,
        mask_ratio=0.75
    ):
        """
        前向传播函数（主要用于预训练阶段，包含掩码操作）。

        Args:
            x (torch.Tensor): 输入张量，形状为 (B, C, T, H, W)。
            temporal_coords (Optional[torch.Tensor]): 时间坐标信息，形状为 (B, T, 2)。
            location_coords (Optional[torch.Tensor]): 地理位置坐标信息，形状为 (B, 2)。
            mask_ratio (float): 掩码的 Patch 比例。

        Returns:
            tuple:
                - `x`: 经过 Transformer 编码器处理后的特征。
                - `mask`: 应用的二进制掩码。
                - `ids_restore`: 用于恢复原始 Patch 顺序的索引。
        """
        # 动态位置编码：如果当前输入尺寸与模型初始化时的预设尺寸不同，则重新生成位置编码
        if x.shape[-3:] != self.patch_embed.input_size:
            # changed input size
            pos_embed = self._get_pos_embed(x)
        else:
            pos_embed = self.pos_embed # 使用预计算好的位置编码

        # 1. Patch Embedding：将 3D 输入转换为 Patch 序列 (B, C, T, H, W) -> (B, num_patches, embed_dim)
        x = self.patch_embed(x)

        # 2. 添加空间-时间位置编码 (不包含 cls token 的部分)
        x = x + pos_embed[:, 1:, :]

        # 3. 添加时间编码 (如果启用)
        if self.temporal_encoding:
            num_tokens_per_frame = x.shape[1] // self.num_frames # 计算每帧有多少个 Patch
            temporal_encoding = self.temporal_embed_enc(temporal_coords, num_tokens_per_frame) # 生成时间编码
            x = x + temporal_encoding # 将时间编码加到 Patch 嵌入上
        
        # 4. 添加地理位置编码 (如果启用)
        if self.location_encoding:
            location_encoding = self.location_embed_enc(location_coords)
            x = x + location_encoding # 将地理位置编码加到 Patch 嵌入上

        # 5. 随机掩码  masking: length -> length * mask_ratio
        x, mask, ids_restore = self.random_masking(x, mask_ratio)

        # 6. 添加 Class Token
        cls_token = self.cls_token + pos_embed[:, :1, :] # 将 Class Token 的位置编码加到 Class Token 本身
        cls_tokens = cls_token.expand(x.shape[0], -1, -1) # 将 Class Token 扩展到批次大小
        x = torch.cat((cls_tokens, x), dim=1) # 将 Class Token 拼接到 Patch 序列的前面 
        # 最终形状 (B, num_unmasked_patches + 1, embed_dim)

        # 7. 通过 Transformer 编码器块
        for block in self.blocks:
            x = block(x)
        # 8. 最终的 LayerNorm
        x = self.norm(x)

        return x, mask, ids_restore

    def forward_features(
        self,
        x: torch.Tensor,
        temporal_coords: None | torch.Tensor = None,
        location_coords: None | torch.Tensor = None,
    ) -> list[torch.Tensor]:
        """
        提取特征的前向传播方法，不进行掩码操作
        
        Args:
            x: 输入张量，形状为(batch_size, num_channels, time, height, width)
            temporal_coords: 时间坐标信息，可选
            location_coords: 位置坐标信息，可选
            
        Returns:
            包含各Transformer层输出特征的列表
        """
        # 兼容 4D 输入 (例如单个图像，没有明确的时间维度)
        if len(x.shape) == 4 and self.patch_embed.input_size[0] == 1:
            # add time dim (B, C, H, W) -> (B, C, 1, H, W)
            x = x.unsqueeze(2)

        # 动态位置编码：与 forward 函数中逻辑相同
        if x.shape[-3:] != self.patch_embed.input_size:
            pos_embed = self._get_pos_embed(x)
        else:
            pos_embed = self.pos_embed

        # 1. Patch Embedding
        x = self.patch_embed(x)

        # 2. 添加空间-时间位置编码
        x = x + pos_embed[:, 1:, :]

        # 3. 添加时间编码 (如果启用)
        if self.temporal_encoding:
            num_tokens_per_frame = x.shape[1] // self.patch_embed.num_frames
            temporal_encoding = self.temporal_embed_enc(temporal_coords, num_tokens_per_frame)
            x = x + temporal_encoding
        # 4. 添加地理位置编码 (如果启用)
        if self.location_encoding:
            location_encoding = self.location_embed_enc(location_coords)
            x = x + location_encoding

        # 5. 添加 Class Token
        cls_token = self.cls_token + pos_embed[:, :1, :]
        cls_tokens = cls_token.expand(x.shape[0], -1, -1)
        x = torch.cat((cls_tokens, x), dim=1)

        # 6. 应用 Transformer 编码器块，并收集每层的输出
        out = []
        for block in self.blocks:
            x = block(x)
            out.append(x.clone()) # 收集中间层输出
        # 7. 最终的 LayerNorm
        x = self.norm(x)
        out[-1] = x # 用最终的 LayerNorm 输出更新最后一层的特征
        return out

    def prepare_features_for_image_model(self, features: list[torch.Tensor]) -> list[torch.Tensor]:
        """
        将特征准备为适合图像模型使用的格式
        将从 Transformer 编码器中获取的序列化特征，重新排列成适合传统图像模型（如 U-Net 解码器）使用的空间形式
        
        Args:
            features: 从forward_features方法获取的特征列表
            
        Returns:
            重新排列后适合图像处理的特征列表，将序列形式的特征转换为空间形式
        """
        out = []
        # 计算时间维度上 Patch 网格的数量 (T_grid) 即根据对应于patch大小的卷积核形状决定（T维度一般默认是1）
        # 即初始化所输入的时间帧/patch所包含的时间帧
        effective_time_dim = self.patch_embed.input_size[0] // self.patch_embed.patch_size[0] 
        for x in features:
            # 移除 Class Token，只保留 Patch 嵌入
            x_no_token = x[:, 1:, :] # 形状 (B, num_patches, embed_dim)
            number_of_tokens = x_no_token.shape[1] # 总的 Patch 数量
            # 计算每个时间步（帧）的空间 Patch 数量 (H_grid * W_grid)
            tokens_per_timestep = number_of_tokens // effective_time_dim
            # 从空间 Patch 数量推断出 H_grid (假设是正方形网格)
            h = int(np.sqrt(tokens_per_timestep))
            # 使用 einops.rearrange 重新排列张量
            # 将 Transformer 输出的序列化特征重新构造成类似卷积网络可以处理的 (B, C, H, W) 形
            # 但这里 C 变成了 (时间维度 * 嵌入维度)，因为它同时包含了时间和特征信息
            # 类似于将多时间下的特征合并使用？
            encoded = rearrange(
                x_no_token,
                "batch (t h w) e -> batch (t e) h w",
                e=self.embed_dim,
                t=effective_time_dim,
                h=h,
            ) 

            out.append(encoded)
        return out