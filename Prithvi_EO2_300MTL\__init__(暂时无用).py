
"""
Prithvi MAE模型包
Prithvi MAE model package
"""

# 告诉程序这是一个包使用
# 从重构后的模块导入所需的类
from models.prithvi_mae import PrithviMAE
from models.encoder import PrithviViT
from models.decoder import MAEDecoder
from models.patch_embed import PatchEmbed
from models.coordinate_encoders import TemporalEncoder, LocationEncoder
from utils.position_encoding import (
    get_3d_sincos_pos_embed,
    get_1d_sincos_pos_embed_from_grid,
    _get_1d_sincos_embed_from_grid_torch,
    _interpolate_pos_encoding
)
from utils.model_utils import _init_weights

# 导出所有类和函数，保持与原始文件相同的接口
__all__ = [
    # models中类
    'PrithviMAE',
    'PrithviViT',
    'MAEDecoder',
    'PatchEmbed',
    'TemporalEncoder',
    'LocationEncoder',
    
    # utils中孤立函数
    'get_3d_sincos_pos_embed',
    'get_1d_sincos_pos_embed_from_grid',
    '_get_1d_sincos_embed_from_grid_torch',
    '_interpolate_pos_encoding',
    '_init_weights'
]
