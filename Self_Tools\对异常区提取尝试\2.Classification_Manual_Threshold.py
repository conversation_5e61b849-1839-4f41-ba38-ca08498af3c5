import os
import glob
import numpy as np
import rasterio
import argparse
from rasterio.profiles import DefaultGTiffProfile
from typing import Tuple, Dict, Any, Optional

# --- 配置 ---
INPUT_FOLDER = "Average_Loss_tiff"
BINARY_MASK_FOLDER = "MyModel_Mask_Result"

# --- 阈值确定方法 ---
def calculate_threshold_manual(image_data: np.ndarray, threshold_value: float) -> Tuple[float, np.ndarray]:
    """
    使用手动设定的固定阈值值进行二值化
    
    参数:
        image_data: 输入图像数据
        threshold_value: 手动设定的阈值
        
    返回:
        threshold: 使用的阈值
        binary_mask: 二值化掩码
    """
    threshold = threshold_value
    binary_mask = (image_data > threshold).astype(np.uint8)
    return threshold, binary_mask

def calculate_threshold_percentile(image_data: np.ndarray, percentile_level: float) -> Tuple[float, np.ndarray]:
    """
    使用分位数方法确定阈值进行二值化
    
    参数:
        image_data: 输入图像数据
        percentile_level: 分位数值 (0-100)
        
    返回:
        threshold: 计算得到的阈值
        binary_mask: 二值化掩码
    """
    threshold = np.percentile(image_data.ravel(), percentile_level)
    binary_mask = (image_data > threshold).astype(np.uint8)
    return threshold, binary_mask

def calculate_threshold(image_data: np.ndarray, method: str, **kwargs) -> Tuple[float, np.ndarray, float]:
    """
    根据指定方法计算阈值并生成二值掩码
    
    参数:
        image_data: 输入图像数据
        method: 阈值确定方法 ('manual', 'percentile')
        **kwargs: 其他参数，包括 threshold_value 或 percentile_level
        
    返回:
        threshold: 计算得到的阈值
        binary_mask: 二值化掩码
        anomaly_percentage: 异常像素占比
    """
    # 获取图像数据的实际范围
    min_val = np.min(image_data)
    max_val = np.max(image_data)
    
    # 检查数据是否为常数，如果是则无法计算阈值
    if min_val == max_val:
        print(" 警告: 图像数据为常数，无法计算阈值。生成全0掩码。")
        binary_mask = np.zeros(image_data.shape, dtype=np.uint8)
        threshold = min_val
        anomaly_percentage = 0.0
    else:
        # 根据选择的方法确定阈值
        if method == 'manual':
            # 使用用户提供的固定阈值
            threshold_value = kwargs.get('threshold_value')
            threshold, binary_mask = calculate_threshold_manual(image_data, threshold_value)
            print(f" 使用手动设定的固定阈值: {threshold:.2f}")
        elif method == 'percentile':
            # 使用分位数方法确定阈值
            percentile_level = kwargs.get('percentile_level', 95)
            threshold, binary_mask = calculate_threshold_percentile(image_data, percentile_level)
            print(f" 使用分位数阈值 ({percentile_level}%): {threshold:.2f}")
        else:
            # 默认使用分位数方法
            percentile_level = kwargs.get('percentile_level', 95)
            threshold, binary_mask = calculate_threshold_percentile(image_data, percentile_level)
            print(f" 使用默认分位数阈值 ({percentile_level}%): {threshold:.2f}")
        
        # 计算掩码中异常像素的比例
        anomaly_percentage = np.mean(binary_mask) * 100
        print(f" 异常像素占比: {anomaly_percentage:.2f}%")
    
    return threshold, binary_mask, anomaly_percentage

# --- 命令行参数解析 ---
def parse_args():
    parser = argparse.ArgumentParser(description='对残差图进行二值化分离，生成异常掩码')
    parser.add_argument('--input_folder', type=str, default=INPUT_FOLDER,
                        help=f'输入文件夹路径，默认为 {INPUT_FOLDER}')
    parser.add_argument('--output_folder', type=str, default=BINARY_MASK_FOLDER,
                        help=f'输出文件夹路径，默认为 {BINARY_MASK_FOLDER}')
    parser.add_argument('--method', type=str, choices=['manual', 'percentile'], default='percentile',
                        help='阈值确定方法: manual(手动设定固定阈值) 或 percentile(分位数方法)')
    parser.add_argument('--threshold', type=float, default=None,
                        help='当 method=manual 时使用的固定阈值值')
    parser.add_argument('--percentile', type=float, default=95,
                        help='当 method=percentile 时使用的分位数值 (0-100)，默认为95')
    return parser.parse_args()

# --- 保存掩码函数 ---
def save_binary_mask(binary_mask: np.ndarray, profile: Dict[str, Any], output_path: str) -> None:
    """
    保存二值掩码为GeoTIFF文件
    
    参数:
        binary_mask: 二值掩码数据
        profile: 原始图像的配置信息
        output_path: 输出文件路径
    """
    binary_profile = profile.copy()
    binary_profile.update(
        dtype=rasterio.uint8,
        count=1,
        nodata=None  # 或者根据需要设置nodata值，例如 255
    )
    # 确保 'blockxsize' 和 'blockysize' 存在且有效 (对于某些驱动如COG)
    if 'blockxsize' not in binary_profile or not isinstance(binary_profile.get('blockxsize'), int) or binary_profile.get('blockxsize', 0) <= 0:
        binary_profile['blockxsize'] = min(512, profile['width'])  # 设置默认块大小
    if 'blockysize' not in binary_profile or not isinstance(binary_profile.get('blockysize'), int) or binary_profile.get('blockysize', 0) <= 0:
        binary_profile['blockysize'] = min(512, profile['height'])  # 设置默认块大小
    # 移除可能不兼容uint8的选项
    binary_profile.pop('compress', None)  # LZW等可能不支持uint8很好，或者让rasterio自动选择
    binary_profile.pop('predictor', None)

    with rasterio.open(output_path, 'w', **binary_profile) as dst:
        dst.write(binary_mask, 1)
    print(f"  二值掩码已保存到: {output_path}")

# --- 处理单个文件函数 ---
def process_file(tiff_path: str, output_dir: str, method: str, **kwargs) -> bool:
    """
    处理单个TIFF文件，生成二值掩码
    
    参数:
        tiff_path: 输入TIFF文件路径
        output_dir: 输出目录
        method: 阈值确定方法
        **kwargs: 其他参数，包括 threshold_value 或 percentile_level
        
    返回:
        bool: 处理是否成功
    """
    file_name = os.path.basename(tiff_path)
    print(f"\n--- 正在处理文件: {file_name} ---")
    
    try:
        # --- 读取原始 GeoTIFF ---
        with rasterio.open(tiff_path) as src:
            profile = src.profile
            image_data = src.read(1)
            original_dtype = profile['dtype']  # 保存原始数据类型
            print(f"  图像尺寸: {image_data.shape}, 原始数据类型: {original_dtype}")

            if image_data.size == 0:
                print("  错误：读取的图像数据为空。")
                return False

            # --- 步骤: 生成二值化异常掩码 ---
            print(" 生成二值化异常掩码...")

            try:
                # 调用阈值计算函数
                threshold, binary_mask, _ = calculate_threshold(image_data, method, **kwargs)
                
            except Exception as e_threshold:
                # 捕获阈值计算过程中的任何错误
                print(f" 错误: 在阈值确定步骤发生错误: {e_threshold}")
                print(" 无法确定阈值，生成全0掩码。")
                # 发生严重错误，无法计算阈值，生成一个全0的掩码作为安全回退
                binary_mask = np.zeros(image_data.shape, dtype=np.uint8)
                threshold = np.min(image_data)  # 或者None，表示阈值未知/无效

            # --- 保存二值掩码 ---
            binary_mask_path = os.path.join(output_dir, file_name.replace('.tif', '_Mask.tif'))
            save_binary_mask(binary_mask, profile, binary_mask_path)
            
            return True

    except rasterio.RasterioIOError as e_rio:
        print(f"  错误: 无法读取或写入文件 {file_name}. Rasterio Error: {e_rio}")
    except Exception as e:
        print(f"  处理文件 {file_name} 时发生未知错误: {e}")
        import traceback
        traceback.print_exc()  # 打印详细错误堆栈
    
    return False

# --- 主函数 ---
def main():
    """
    主函数，处理命令行参数并执行批处理
    """
    # 解析命令行参数
    args = parse_args()
    
    # 获取当前工作目录
    base_dir = os.getcwd()
    print(f"当前工作目录是：{base_dir}")

    # 配置输入输出路径
    input_dir = os.path.join(base_dir, args.input_folder)
    binary_mask_dir = os.path.join(base_dir, args.output_folder)

    # 创建输出文件夹
    os.makedirs(binary_mask_dir, exist_ok=True)

    # 查找所有输入tiff文件
    tiff_files = glob.glob(os.path.join(input_dir, "*.tif*"))  # 支持 .tif 和 .tiff
    if not tiff_files:
        print(f"错误：在文件夹 '{input_dir}' 中未找到 .tif 或 .tiff 文件。")
        return False

    print(f"找到 {len(tiff_files)} 个待处理文件...")
    print(f"使用阈值方法: {args.method}")
    
    # 如果是手动阈值模式但未提供阈值，则提示错误并退出
    if args.method == 'manual' and args.threshold is None:
        print("错误：选择了手动阈值模式，但未提供阈值值。请使用 --threshold 参数指定阈值。")
        return False
    elif args.method == 'manual':
        print(f"使用手动设定的固定阈值: {args.threshold}")
    else:
        print(f"使用分位数阈值: {args.percentile}%")
    
    # 准备阈值计算参数
    threshold_params = {}
    if args.method == 'manual':
        threshold_params['threshold_value'] = args.threshold
    elif args.method == 'percentile':
        threshold_params['percentile_level'] = args.percentile
    
    # 处理每个文件
    success_count = 0
    for tiff_path in tiff_files:
        if process_file(tiff_path, binary_mask_dir, args.method, **threshold_params):
            success_count += 1
    
    print(f"\n--- 所有文件处理完成 ---")
    print(f"成功处理: {success_count}/{len(tiff_files)} 个文件")
    
    print("\n使用方法示例:")
    print("1. 使用分位数方法 (默认95%分位数):")
    print("   python 2.Classification_Manual_Threshold.py")
    print("2. 使用分位数方法并指定80%分位数:") # 然后尝试极端情况时的对应阈值[75-98]
    print("   python 2.Classification_Manual_Threshold.py --method percentile --percentile 99")
    print("3. 使用手动设定的固定阈值 20w:")
    print("   python 2.Classification_Manual_Threshold.py --method manual --threshold 200000")
    
    return True

# --- 主要处理流程 ---
if __name__ == '__main__':
    main()