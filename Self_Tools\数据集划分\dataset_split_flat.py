#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集扁平化划分脚本
将source_dir中的所有图像文件（不管原有文件结构）按比例直接存储到
train_images、val_images、test_images文件夹中，不保持原有目录结构
"""

import os
import shutil
import random
from pathlib import Path
from datetime import datetime
import json

def find_all_images(source_dir, extensions=('.tif', '.tiff', '.jpg', '.jpeg', '.png', '.bmp', '.gif')):
    """
    递归查找source_dir中的所有图像文件
    
    Args:
        source_dir: 源数据目录
        extensions: 支持的图像文件扩展名
    
    Returns:
        list: 所有图像文件的完整路径列表
    """
    image_files = []
    
    for root, dirs, files in os.walk(source_dir):
        for file in files:
            if file.lower().endswith(extensions):
                full_path = os.path.join(root, file)
                image_files.append(full_path)
    
    return image_files

def create_flat_directory_structure(output_dir):
    """创建扁平化输出目录结构"""
    train_dir = os.path.join(output_dir, 'train_images')
    val_dir = os.path.join(output_dir, 'val_images')
    test_dir = os.path.join(output_dir, 'test_images')
    
    for dir_path in [train_dir, val_dir, test_dir]:
        os.makedirs(dir_path, exist_ok=True)
    
    return train_dir, val_dir, test_dir

def generate_unique_filename(filename, existing_files):
    """
    生成唯一的文件名，避免重名冲突
    
    Args:
        filename: 原始文件名
        existing_files: 已存在的文件名集合
    
    Returns:
        str: 唯一的文件名
    """
    if filename not in existing_files:
        return filename
    
    name, ext = os.path.splitext(filename)
    counter = 1
    
    while True:
        new_filename = f"{name}_{counter:03d}{ext}"
        if new_filename not in existing_files:
            return new_filename
        counter += 1

def split_dataset_flat(source_dir, output_dir, train_ratio=0.75, val_ratio=0.15, test_ratio=0.10, seed=42):
    """
    扁平化划分数据集
    
    Args:
        source_dir: 源数据目录
        output_dir: 输出目录
        train_ratio: 训练集比例
        val_ratio: 验证集比例
        test_ratio: 测试集比例
        seed: 随机种子
    """
    # 设置随机种子
    random.seed(seed)
    
    # 创建输出目录结构
    train_dir, val_dir, test_dir = create_flat_directory_structure(output_dir)
    
    # 查找所有图像文件
    print("正在搜索所有图像文件...")
    all_image_files = find_all_images(source_dir)
    
    if not all_image_files:
        print(f"错误: 在 {source_dir} 中没有找到任何图像文件!")
        return None
    
    print(f"找到 {len(all_image_files)} 个图像文件")
    
    # 随机打乱文件列表
    random.shuffle(all_image_files)
    
    # 计算划分点
    total_count = len(all_image_files)
    train_count = int(total_count * train_ratio)
    val_count = int(total_count * val_ratio)
    test_count = total_count - train_count - val_count
    
    # 划分文件
    train_files = all_image_files[:train_count]
    val_files = all_image_files[train_count:train_count + val_count]
    test_files = all_image_files[train_count + val_count:]
    
    print(f"数据集划分:")
    print(f"  训练集: {len(train_files)} 个文件 ({len(train_files)/total_count*100:.2f}%)")
    print(f"  验证集: {len(val_files)} 个文件 ({len(val_files)/total_count*100:.2f}%)")
    print(f"  测试集: {len(test_files)} 个文件 ({len(test_files)/total_count*100:.2f}%)")
    
    # 用于跟踪文件名冲突
    train_existing = set()
    val_existing = set()
    test_existing = set()
    
    # 统计信息
    split_info = {
        'split_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'random_seed': seed,
        'split_ratios': {
            'train': train_ratio,
            'validation': val_ratio,
            'test': test_ratio
        },
        'source_directory': source_dir,
        'output_directory': output_dir,
        'total_files': total_count,
        'train_files': len(train_files),
        'val_files': len(val_files),
        'test_files': len(test_files),
        'train_ratio_actual': len(train_files) / total_count,
        'val_ratio_actual': len(val_files) / total_count,
        'test_ratio_actual': len(test_files) / total_count,
        'file_conflicts': {
            'train': 0,
            'val': 0,
            'test': 0
        }
    }
    
    # 复制训练集文件
    print("正在复制训练集文件...")
    for i, src_path in enumerate(train_files):
        filename = os.path.basename(src_path)
        unique_filename = generate_unique_filename(filename, train_existing)
        
        if unique_filename != filename:
            split_info['file_conflicts']['train'] += 1
        
        train_existing.add(unique_filename)
        dst_path = os.path.join(train_dir, unique_filename)
        shutil.copy2(src_path, dst_path)
        
        if (i + 1) % 1000 == 0:
            print(f"  已复制 {i + 1}/{len(train_files)} 个训练集文件")
    
    # 复制验证集文件
    print("正在复制验证集文件...")
    for i, src_path in enumerate(val_files):
        filename = os.path.basename(src_path)
        unique_filename = generate_unique_filename(filename, val_existing)
        
        if unique_filename != filename:
            split_info['file_conflicts']['val'] += 1
        
        val_existing.add(unique_filename)
        dst_path = os.path.join(val_dir, unique_filename)
        shutil.copy2(src_path, dst_path)
        
        if (i + 1) % 1000 == 0:
            print(f"  已复制 {i + 1}/{len(val_files)} 个验证集文件")
    
    # 复制测试集文件
    print("正在复制测试集文件...")
    for i, src_path in enumerate(test_files):
        filename = os.path.basename(src_path)
        unique_filename = generate_unique_filename(filename, test_existing)
        
        if unique_filename != filename:
            split_info['file_conflicts']['test'] += 1
        
        test_existing.add(unique_filename)
        dst_path = os.path.join(test_dir, unique_filename)
        shutil.copy2(src_path, dst_path)
        
        if (i + 1) % 1000 == 0:
            print(f"  已复制 {i + 1}/{len(test_files)} 个测试集文件")
    
    return split_info

def generate_flat_report(split_info, output_dir):
    """生成扁平化数据划分报告"""
    report_path = os.path.join(output_dir, 'dataset_flat_split_report.md')
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("# 数据集扁平化划分报告\n\n")
        
        f.write("## 划分配置\n")
        f.write(f"- **划分时间**: {split_info['split_time']}\n")
        f.write(f"- **随机种子**: {split_info['random_seed']}\n")
        f.write(f"- **源目录**: {split_info['source_directory']}\n")
        f.write(f"- **输出目录**: {split_info['output_directory']}\n")
        f.write(f"- **划分比例**: 训练集 {split_info['split_ratios']['train']*100:.1f}%, ")
        f.write(f"验证集 {split_info['split_ratios']['validation']*100:.1f}%, ")
        f.write(f"测试集 {split_info['split_ratios']['test']*100:.1f}%\n\n")
        
        f.write("## 总体统计\n")
        f.write(f"- **总文件数**: {split_info['total_files']}\n")
        f.write(f"- **训练集**: {split_info['train_files']} 个文件 ({split_info['train_ratio_actual']*100:.2f}%)\n")
        f.write(f"- **验证集**: {split_info['val_files']} 个文件 ({split_info['val_ratio_actual']*100:.2f}%)\n")
        f.write(f"- **测试集**: {split_info['test_files']} 个文件 ({split_info['test_ratio_actual']*100:.2f}%)\n\n")
        
        f.write("## 文件名冲突处理\n")
        total_conflicts = sum(split_info['file_conflicts'].values())
        f.write(f"- **总冲突数**: {total_conflicts}\n")
        f.write(f"- **训练集冲突**: {split_info['file_conflicts']['train']}\n")
        f.write(f"- **验证集冲突**: {split_info['file_conflicts']['val']}\n")
        f.write(f"- **测试集冲突**: {split_info['file_conflicts']['test']}\n\n")
        
        f.write("## 数据划分方式说明\n")
        f.write("1. **扁平化存储**: 所有图像文件直接存储在train_images、val_images、test_images文件夹中\n")
        f.write("2. **递归搜索**: 自动搜索源目录及其所有子目录中的图像文件\n")
        f.write("3. **随机划分**: 使用随机种子确保结果可重现\n")
        f.write("4. **文件复制**: 原始文件保持不变，在目标目录创建副本\n")
        f.write("5. **冲突处理**: 自动处理重名文件，添加数字后缀\n")
        f.write("6. **支持格式**: .tif, .tiff, .jpg, .jpeg, .png, .bmp, .gif\n\n")
    
    # 同时保存JSON格式的详细信息
    json_path = os.path.join(output_dir, 'dataset_flat_split_info.json')
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(split_info, f, ensure_ascii=False, indent=2)
    
    print(f"报告已生成: {report_path}")
    print(f"详细信息已保存: {json_path}")

def main():
    """主函数"""
    # 配置路径 - 请根据实际情况修改
    source_dir = "EuroSATall_Bands_sentinel_2"  # 源数据目录
    output_dir = "Data_after_FlatSplit"         # 输出目录
    
    # 检查源目录是否存在
    if not os.path.exists(source_dir):
        print(f"错误: 源目录 {source_dir} 不存在!")
        print("请修改 source_dir 变量为正确的源数据目录路径")
        return
    
    print("开始扁平化数据集划分...")
    print(f"源目录: {source_dir}")
    print(f"输出目录: {output_dir}")
    print("划分比例: 训练集75%, 验证集15%, 测试集10%")
    print("随机种子: 42")
    print("存储方式: 扁平化（所有文件直接存储在对应文件夹中）")
    print("-" * 60)
    
    # 执行数据集划分
    split_info = split_dataset_flat(
        source_dir=source_dir,
        output_dir=output_dir,
        train_ratio=0.75,
        val_ratio=0.15,
        test_ratio=0.10,
        seed=42
    )
    
    if split_info is None:
        print("数据集划分失败!")
        return
    
    # 生成报告
    generate_flat_report(split_info, output_dir)
    
    print("-" * 60)
    print("扁平化数据集划分完成!")
    print(f"总共处理了 {split_info['total_files']} 个文件")
    print(f"训练集: {split_info['train_files']} 个文件")
    print(f"验证集: {split_info['val_files']} 个文件")
    print(f"测试集: {split_info['test_files']} 个文件")
    
    total_conflicts = sum(split_info['file_conflicts'].values())
    if total_conflicts > 0:
        print(f"处理了 {total_conflicts} 个文件名冲突")

if __name__ == "__main__":
    main()
