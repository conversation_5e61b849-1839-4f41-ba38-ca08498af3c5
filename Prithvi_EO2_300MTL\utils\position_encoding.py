
"""
位置编码相关的工具函数
Position encoding utilities for Prithvi model
"""

import numpy as np
import torch
import torch.nn as nn

# 创建3D正弦/余弦位置编码 (不直接生成3D位置编码，而是先把3D问题拆分成3个独立的1D问题)
def get_3d_sincos_pos_embed(embed_dim, grid_size, add_cls_token=False):
    """
    创建3D正弦/余弦位置编码
    Create 3D sin/cos positional embeddings.

    Args:
        embed_dim (int):
            Embedding dimension.
            嵌入维度。
        grid_size (tuple[int, int, int] | list[int]):
            The grid depth, height and width.
            网格的时间维深度、高度和宽度。
        add_cls_token (bool, *optional*, defaults to False):
            Whether or not to add a classification (CLS) token.
            是否添加分类（CLS）标记。

    Returns:
        (`torch.FloatTensor` of shape (grid_size[0]*grid_size[1]*grid_size[2], embed_dim) or
        (1+grid_size[0]*grid_size[1]*grid_size[2], embed_dim): the position embeddings (with or without cls token)
        形状为(grid_size[0]*grid_size[1]*grid_size[2], embed_dim)或
        (1+grid_size[0]*grid_size[1]*grid_size[2], embed_dim)的位置编码（有或没有cls标记）
    """

    assert embed_dim % 16 == 0 # 确保嵌入维度是16的倍数

    t_size, h_size, w_size = grid_size # 解包网格尺寸，分别获取时间维深度、高度和时间
    
    w_embed_dim = embed_dim // 16 * 6 # 宽度占6份
    h_embed_dim = embed_dim // 16 * 6 # 高度占6份
    t_embed_dim = embed_dim // 16 * 4 # 时间占4份

    w_pos_embed = get_1d_sincos_pos_embed_from_grid(w_embed_dim, np.arange(w_size)) # 生成正弦/余弦位置编码
    h_pos_embed = get_1d_sincos_pos_embed_from_grid(h_embed_dim, np.arange(h_size))
    t_pos_embed = get_1d_sincos_pos_embed_from_grid(t_embed_dim, np.arange(t_size))

    w_pos_embed = np.tile(w_pos_embed, (t_size * h_size, 1)) # 宽度位置编码，复制 t_size * h_size 次，以便覆盖所有时间和高度位置
    h_pos_embed = np.tile(np.repeat(h_pos_embed, w_size, axis=0), (t_size, 1))  # 高度位置编码，先沿宽度方向重复，再沿时间方向复制
    t_pos_embed = np.repeat(t_pos_embed, h_size * w_size, axis=0) # 时间编码将其沿着高度和宽度方向重复

    pos_embed = np.concatenate((w_pos_embed, h_pos_embed, t_pos_embed), axis=1) # 将3个编码沿列方向拼接

    # 如果需要添加分类（CLS）标记，就在位置编码数组的开头添加一个全零的向量。
    # CLS token通常用于聚合整个序列的信息，其自身没有特定的空间位置，因此其位置编码通常初始化为零或可学习的参数。
    if add_cls_token:
        pos_embed = np.concatenate([np.zeros([1, embed_dim]), pos_embed], axis=0)
    return pos_embed

# 生成 1D 位置编码
def get_1d_sincos_pos_embed_from_grid(embed_dim, pos):
    """
    生成 1D 位置编码
    embed_dim: output dimension for each position pos: a list of positions to be encoded: size (M,) out: (M, D)
    embed_dim: 每个位置的输出维度 pos: 要编码的位置列表：大小为(M,) 输出: (M, D)
    """
    if embed_dim % 2 != 0: #  确保嵌入维度是偶数。正弦/余弦位置编码通常将维度分成两半，一半用于正弦，一半用于余弦
        raise ValueError("embed_dim must be even")

    # 计算位置编码的维度，用于后续正余弦编码的生成
    omega = np.arange(embed_dim // 2, dtype=float) # 决定正弦和余弦波的周期（正弦余弦各占一半维度）# 生成 [0, 1, ..., D/2 - 1]
    omega /= embed_dim/2.0 # 归一化到 [0, 1) 区间，例如 [0, 1/D, 2/D, ...] 即将 omega 中的每个值除以 (embed_dim/2.0)
    omega = 1.0 / 10000**omega  # (D/2,) 即将 omega 中的每个值除以 10000 的 omega 次方

    pos = pos.reshape(-1)  # (M,) # 将位置索引展平为一维数组，确保其形状为 (M,)
    # (M, D/2), outer product 。执行外积，结果是一个矩阵
    # ->md: 这部分指定了输出张量的维度。它告诉 einsum，输出应该是一个二维矩阵，第一个维度是 m（位置），第二个维度是 d（频率/维度）。
    out = np.einsum("m,d->md", pos, omega)  

    emb_sin = np.sin(out)  # (M, D/2)
    emb_cos = np.cos(out)  # (M, D/2)

    emb = np.concatenate([emb_sin, emb_cos], axis=1)  # (M, D) 将正弦和余弦分量(形状 M, D/2)沿特征维度（axis=1）拼接起来，形成最终的位置编码
    return emb

# 生成 1D 位置编码(pytorch版)
def _get_1d_sincos_embed_from_grid_torch(embed_dim: int, pos: torch.Tensor):
    """ 
        生成 1D 位置编码(pytorch版)

        This is the torch version of *get_1d_sincos_pos_embed_from_grid()*. However,
        it was modified to cast omega values to pos.dtype which must be float (and not int as in
        regular positional embeddings). This was required in order to allow for native FSDP mixed
        precision support: modify omega to appropriate dtype (pos carries the correct float dtype),
        instead of manually forcing float32.
        
        这是*get_1d_sincos_pos_embed_from_grid()*的PyTorch版本。然而，
        它被修改为将omega值转换为pos.dtype，必须是浮点类型（而不是常规位置编码中的整数类型）。
        这是为了支持原生FSDP混合精度：将omega修改为适当的数据类型（pos携带正确的浮点数据类型），
        而不是手动强制使用float32。

        embed_dim: output dimension for each position
        pos: a list of positions to be encoded: size (M,) - must be float dtype!
        out: (M, D)
        
        embed_dim: 每个位置的输出维度
        pos: 要编码的位置列表：大小为(M,) - 必须是浮点数据类型！
        out: (M, D)
    """
    assert embed_dim % 2 == 0
    assert pos.dtype in [torch.float32, torch.float16, torch.bfloat16]

    omega = torch.arange(embed_dim // 2, dtype=pos.dtype).to(pos.device)
    omega /= embed_dim / 2.0
    omega = 1.0 / 10000**omega  # (D/2,)

    pos = pos.reshape(-1)  # (M,)
    out = torch.einsum("m,d->md", pos, omega)  # (M, D/2), outer product

    emb_sin = torch.sin(out)  # (M, D/2)
    emb_cos = torch.cos(out)  # (M, D/2)

    emb = torch.cat([emb_sin, emb_cos], dim=1)  # (M, D)

    return emb

# 位置编码插值
def _interpolate_pos_encoding(
        pos_embed: torch.Tensor,
        grid_size: tuple[int, int, int] | list[int], # 原始（预训练）模型所使用的网格尺寸（时间维深度、高度、宽度）
        patch_size: tuple[int, int, int] | list[int], # 形状为 (T, H, W)
        shape: tuple[int, int, int], # 当前输入图像或视频的实际尺寸（时间帧、高度、宽度）
        embed_dim: int,
):
    """
    位置编码插值
    Adapted from:
    - transformers.models.vit.modeling_vit.ViTEmbeddings.interpolate_pos_encoding,
    - https://github.com/facebookresearch/dino/blob/de9ee3df6cf39fac952ab558447af1fa1365362a/vision_transformer.py#L174-L194
    
    插值位置编码，用于处理输入尺寸变化时的位置编码调整。
    当输入图像尺寸与预训练模型的尺寸不同时，需要对位置编码进行插值。
    """
    t, h, w = shape # 解包当前输入视频/图像的尺寸
    t_patches = t // patch_size[0] # 计算输入图像在既有patch上的分隔数
    h_patches = h // patch_size[1]
    w_patches = w // patch_size[2]
    # --- 步骤 1: 检查是否需要插值 ---
    if [t_patches, h_patches, w_patches] == grid_size:
        # 无需插值，直接返回原始位置编码
        return pos_embed
    # --- 步骤 2: 处理时间维深度维度的变化（优先于空间维度进行处理，若帧数改变，会重新计算整个3D位置编码） ---
    if t_patches != grid_size[0]: # 如果当前深度（t_patches）与预训练的深度（grid_size[0]）不同
        # Re-compute pos embedding to handle changed num_frames
        new_grid_size = (t_patches, *grid_size[1:]) # 保持空间维度不变，只更新时间维度
        new_pos_embed = get_3d_sincos_pos_embed(pos_embed.shape[-1], new_grid_size, add_cls_token=True) # 重新生成新的 3D 位置编码
        new_pos_embed = torch.from_numpy(new_pos_embed).float().unsqueeze(0) # 转换为Tensor，并添加一个批次维度 应为（B、N(序列数)、D(向量维度)）
    else:
        # 如果时间维深度相同，则沿用原始位置编码作为基础进行后续处理
        new_grid_size = grid_size
        new_pos_embed = pos_embed # 形状为 (B、N、D)
    # --- 步骤 3: 分离 CLS token 和 patch token 的位置编码 --- (B、N、1)和(B、N、D-1)，即提取向量维度内的编码
    class_pos_embed, patch_pos_embed = new_pos_embed[:, :1], new_pos_embed[:, 1:] # 提取 CLS token 和 patch token 的位置编码
    # --- 步骤 4: 重塑 patch 位置编码以便进行空间插值 --- 
    # 这里的patch_pos_embed.reshape(*new_grid_size, D-1)即相当于对patch_pos_embed进行重塑
    # 即将(B、N、D-1) -> (T, H, W, D-1) 然后再调整顺序至 (T, D-1, H, W) 
    patch_pos_embed = patch_pos_embed.reshape(*new_grid_size, embed_dim).permute(0, 3, 1, 2)
    # --- 步骤 5: 执行双三次插值 ---
    patch_pos_embed = nn.functional.interpolate(
        patch_pos_embed,
        size=(h_patches, w_patches), # 目标高度和宽度上的 patch 数量
        mode='bicubic',
        align_corners=True,
    )
    # --- 步骤 6: 恢复 patch 位置编码的原始形状 --- 
    patch_pos_embed = patch_pos_embed.permute(0, 2, 3, 1).view(1, -1, embed_dim)
    # --- 步骤 7: 拼接 CLS token 和插值后的 patch token 位置编码 ---
    return torch.cat((class_pos_embed, patch_pos_embed), dim=1)