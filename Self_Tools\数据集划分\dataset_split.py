#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EuroSAT数据集划分脚本
按照训练集75%，验证集15%，测试集15%的比例随机划分数据集
随机种子设置为42以确保结果可重现
"""

import os
import shutil
import random
from pathlib import Path
from datetime import datetime
import json

def create_directory_structure(output_dir):
    """创建输出目录结构"""
    train_dir = os.path.join(output_dir, 'train_images')
    val_dir = os.path.join(output_dir, 'val_images')
    test_dir = os.path.join(output_dir, 'test_images')
    
    for dir_path in [train_dir, val_dir, test_dir]:
        os.makedirs(dir_path, exist_ok=True)
    
    return train_dir, val_dir, test_dir

def split_dataset(source_dir, output_dir, train_ratio=0.75, val_ratio=0.15, test_ratio=0.10, seed=42):
    """
    划分数据集
    
    Args:
        source_dir: 源数据目录
        output_dir: 输出目录
        train_ratio: 训练集比例
        val_ratio: 验证集比例
        test_ratio: 测试集比例
        seed: 随机种子
    """
    # 设置随机种子
    random.seed(seed)
    
    # 创建输出目录结构
    train_dir, val_dir, test_dir = create_directory_structure(output_dir)
    
    # 统计信息
    split_info = {
        'split_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'random_seed': seed,
        'split_ratios': {
            'train': train_ratio,
            'validation': val_ratio,
            'test': test_ratio
        },
        'categories': {}
    }
    
    total_files = 0
    total_train = 0
    total_val = 0
    total_test = 0
    
    # 获取所有类别文件夹
    categories = [d for d in os.listdir(source_dir) 
                 if os.path.isdir(os.path.join(source_dir, d))]
    
    print(f"发现 {len(categories)} 个类别: {categories}")
    
    for category in categories:
        category_path = os.path.join(source_dir, category)
        
        # 创建对应的输出子目录
        train_category_dir = os.path.join(train_dir, category)
        val_category_dir = os.path.join(val_dir, category)
        test_category_dir = os.path.join(test_dir, category)
        
        for dir_path in [train_category_dir, val_category_dir, test_category_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # 获取该类别下的所有文件
        files = [f for f in os.listdir(category_path) 
                if f.lower().endswith(('.tif', '.tiff', '.jpg', '.jpeg', '.png'))]
        
        if not files:
            print(f"警告: 类别 {category} 中没有找到图像文件")
            continue
        
        # 随机打乱文件列表
        random.shuffle(files)
        
        # 计算划分点
        total_count = len(files)
        train_count = int(total_count * train_ratio)
        val_count = int(total_count * val_ratio)
        test_count = total_count - train_count - val_count
        
        # 划分文件
        train_files = files[:train_count]
        val_files = files[train_count:train_count + val_count]
        test_files = files[train_count + val_count:]
        
        # 复制文件到对应目录
        print(f"处理类别 {category}: 总计 {total_count} 个文件")
        print(f"  训练集: {len(train_files)} 个文件")
        print(f"  验证集: {len(val_files)} 个文件")
        print(f"  测试集: {len(test_files)} 个文件")
        
        # 复制训练集文件
        for file in train_files:
            src = os.path.join(category_path, file)
            dst = os.path.join(train_category_dir, file)
            shutil.copy2(src, dst)
        
        # 复制验证集文件
        for file in val_files:
            src = os.path.join(category_path, file)
            dst = os.path.join(val_category_dir, file)
            shutil.copy2(src, dst)
        
        # 复制测试集文件
        for file in test_files:
            src = os.path.join(category_path, file)
            dst = os.path.join(test_category_dir, file)
            shutil.copy2(src, dst)
        
        # 记录统计信息
        split_info['categories'][category] = {
            'total_files': total_count,
            'train_files': len(train_files),
            'val_files': len(val_files),
            'test_files': len(test_files),
            'train_ratio_actual': len(train_files) / total_count,
            'val_ratio_actual': len(val_files) / total_count,
            'test_ratio_actual': len(test_files) / total_count
        }
        
        # 累计统计
        total_files += total_count
        total_train += len(train_files)
        total_val += len(val_files)
        total_test += len(test_files)
    
    # 总体统计
    split_info['overall_statistics'] = {
        'total_files': total_files,
        'train_files': total_train,
        'val_files': total_val,
        'test_files': total_test,
        'train_ratio_actual': total_train / total_files if total_files > 0 else 0,
        'val_ratio_actual': total_val / total_files if total_files > 0 else 0,
        'test_ratio_actual': total_test / total_files if total_files > 0 else 0
    }
    
    return split_info

def generate_report(split_info, output_dir):
    """生成数据划分报告"""
    report_path = os.path.join(output_dir, 'dataset_split_report.md')
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("# EuroSAT数据集划分报告\n\n")
        
        f.write("## 划分配置\n")
        f.write(f"- **划分时间**: {split_info['split_time']}\n")
        f.write(f"- **随机种子**: {split_info['random_seed']}\n")
        f.write(f"- **划分比例**: 训练集 {split_info['split_ratios']['train']*100:.1f}%, ")
        f.write(f"验证集 {split_info['split_ratios']['validation']*100:.1f}%, ")
        f.write(f"测试集 {split_info['split_ratios']['test']*100:.1f}%\n\n")
        
        f.write("## 总体统计\n")
        overall = split_info['overall_statistics']
        f.write(f"- **总文件数**: {overall['total_files']}\n")
        f.write(f"- **训练集**: {overall['train_files']} 个文件 ({overall['train_ratio_actual']*100:.2f}%)\n")
        f.write(f"- **验证集**: {overall['val_files']} 个文件 ({overall['val_ratio_actual']*100:.2f}%)\n")
        f.write(f"- **测试集**: {overall['test_files']} 个文件 ({overall['test_ratio_actual']*100:.2f}%)\n\n")
        
        f.write("## 各类别详细统计\n")
        f.write("| 类别 | 总数 | 训练集 | 验证集 | 测试集 | 训练集比例 | 验证集比例 | 测试集比例 |\n")
        f.write("|------|------|--------|--------|--------|------------|------------|------------|\n")
        
        for category, stats in split_info['categories'].items():
            f.write(f"| {category} | {stats['total_files']} | {stats['train_files']} | ")
            f.write(f"{stats['val_files']} | {stats['test_files']} | ")
            f.write(f"{stats['train_ratio_actual']*100:.2f}% | ")
            f.write(f"{stats['val_ratio_actual']*100:.2f}% | ")
            f.write(f"{stats['test_ratio_actual']*100:.2f}% |\n")
        
        f.write("\n## 数据划分方式说明\n")
        f.write("1. **随机划分**: 使用随机种子42确保结果可重现\n")
        f.write("2. **分层划分**: 每个类别按相同比例进行划分\n")
        f.write("3. **文件复制**: 原始文件保持不变，在目标目录创建副本\n")
        f.write("4. **目录结构**: 保持原有的类别目录结构\n\n")
    
    # 同时保存JSON格式的详细信息
    json_path = os.path.join(output_dir, 'dataset_split_info.json')
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(split_info, f, ensure_ascii=False, indent=2)
    
    print(f"报告已生成: {report_path}")
    print(f"详细信息已保存: {json_path}")

def main():
    """主函数"""
    # 配置路径
    source_dir = "EuroSATall_Bands_sentinel_2"
    output_dir = "Data_after_Randomsplit"
    
    # 检查源目录是否存在
    if not os.path.exists(source_dir):
        print(f"错误: 源目录 {source_dir} 不存在!")
        return
    
    print("开始数据集划分...")
    print(f"源目录: {source_dir}")
    print(f"输出目录: {output_dir}")
    print("划分比例: 训练集75%, 验证集15%, 测试集15%")
    print("随机种子: 42")
    print("-" * 50)
    
    # 执行数据集划分
    split_info = split_dataset(
        source_dir=source_dir,
        output_dir=output_dir,
        train_ratio=0.75,
        val_ratio=0.15,
        test_ratio=0.10,
        seed=42
    )
    
    # 生成报告
    generate_report(split_info, output_dir)
    
    print("-" * 50)
    print("数据集划分完成!")
    print(f"总共处理了 {split_info['overall_statistics']['total_files']} 个文件")
    print(f"训练集: {split_info['overall_statistics']['train_files']} 个文件")
    print(f"验证集: {split_info['overall_statistics']['val_files']} 个文件")
    print(f"测试集: {split_info['overall_statistics']['test_files']} 个文件")

if __name__ == "__main__":
    main()
