
"""
模型工具函数
Model utilities for Prithvi model
"""

import torch
import torch.nn as nn

# 位置编码插值
def _init_weights(module):
    """
    初始化权重
    Initialize the weights
    """
    if isinstance(module, nn.Linear): # 检查当前模块是否是全连接层 (nn.Linear)
        nn.init.xavier_uniform_(module.weight) # Xavier 初始化（使用均匀分布）
        if module.bias is not None:
            module.bias.data.zero_() # 偏置项设置为零
    elif isinstance(module, nn.LayerNorm): # 检查当前模块是否是层归一化层
        module.bias.data.zero_() # 偏置项设置为零
        module.weight.data.fill_(1.0) # 将层归一化层的权重项（gamma，缩放因子）初始化为 1.0