import os
import glob
import rasterio
import numpy as np
from sklearn.metrics import confusion_matrix, roc_curve, auc, f1_score, jaccard_score, cohen_kappa_score # 导入 cohen_kappa_score
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import logging
from datetime import datetime
import sys
import re
from typing import Union

# 导入 rasterio 的重投影和重采样模块
from rasterio.warp import reproject, Resampling

# --- 全局配置常量 ---
PRED_FOLDER = "MyModel_Mask_Result" # 预测文件夹名称
REF_FOLDER = "0.GT_Mask(Binary)" # 参考文件夹名称
OUTPUT_FOLDER = "Evaluation_Results"
LOG_FILE_NAME = "evaluation_log.txt"
ROC_CURVE_FILE_NAME = "average_roc_curve.png"
SEP_MAP_PREFIX = "separability_map_"
SEP_MAP_SUFFIX = ".tiff"

# 标签映射
LABEL_MAP = {0: "Normal", 1: "Anomaly"}
CLASS_LABELS = [LABEL_MAP[0], LABEL_MAP[1]] # ["Normal", "Anomaly"]

# 分离性图例颜色和标签 (对应值 0, 1, 2, 3) (已更新含义)
SEP_COLORS = ['blue', 'red', 'yellow', 'green']
SEP_LABELS = [
    'True Negative (Normal correctly predicted as Normal)',     # TN (Value 0)
    'False Positive (Normal incorrectly predicted as Anomaly)',     # FP (Value 1)
    'False Negative (Anomaly incorrectly predicted as Normal)',     # FN (Value 2)
    'True Positive (Anomaly correctly predicted as Anomaly)'      # TP (Value 3)
]
SEP_CMAP = ListedColormap(SEP_COLORS)
SEP_NODATA = -1 # 分离性图的 NoData 值

# --- 辅助函数 ---
def extract_number_re(filename: str) -> Union[int, None]:
    """
    使用正则表达式从文件名中提取第一个连续的数字序列。

    参数:
        filename: 待处理的文件名字符串。

    返回:
        提取到的整数数字，如果没有找到则返回None。
    """
    import re
    # \d+ 匹配一个或多个数字
    # re.search 从字符串的开头扫描，查找第一个匹配项
    match = re.search(r'\d+', filename)
    if match:
        return int(match.group(0))  # group(0) 返回整个匹配到的字符串
    return None

def setup_environment(output_folder, log_filename):
    """
    设置运行环境：创建输出文件夹并配置日志记录器。

    Args:
        output_folder (str): 输出结果的目标文件夹路径。
        log_filename (str): 日志文件的名称。

    Returns:
        str: 日志文件的完整路径。
    """
    log_filepath = os.path.join(output_folder, log_filename)
    os.makedirs(output_folder, exist_ok=True)

    # 配置日志记录到文件和控制台
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s - %(levelname)s - %(message)s',
                        handlers=[
                            logging.FileHandler(log_filepath, mode='w', encoding='utf-8'), # 写入文件
                            logging.StreamHandler() # 同时输出到控制台
                        ])
    logging.info(f"日志文件初始化成功: {log_filepath}")
    return log_filepath

def find_file_pairs(pred_folder, ref_folder):
    """
    查找预测文件，并尝试匹配对应的参考文件。

    Args:
        pred_folder (str): 预测掩膜所在的文件夹。
        ref_folder (str): 参考掩膜所在的文件夹。

    Returns:
        list[tuple[str, str, str]]: 一个包含有效文件对信息的列表，
                                     每个元组为 (预测文件路径, 参考文件路径, 文件基础名)。
                                     如果找不到预测文件或参考文件夹不存在，则返回空列表。
    """
    if not os.path.isdir(pred_folder):
        logging.error(f"预测文件夹不存在: {pred_folder}")
        return []
    if not os.path.isdir(ref_folder):
        logging.error(f"参考文件夹不存在: {ref_folder}")
        return []

    # 根据您的代码，预测文件名模式是 '*.tif*'
    pred_files = glob.glob(os.path.join(pred_folder, '*.tif*'))
    logging.info(f"在 '{pred_folder}' 中找到 {len(pred_files)} 个预测文件。")

    file_pairs = []
    for pred_path in pred_files:
        pred_filename = os.path.basename(pred_path)
        # 提取配对用数字: xxx_Average*.tiff -> xxx
        base_name = pred_filename.replace('_Average*.tiff', '')
        base_number = extract_number_re(base_name) # 提取其中的数字

        # 构建对应的参考文件名 (参考掩膜的文件名是 'mask_'+ 数字 + .tiff")
        ref_filename_expected = f"mask_{base_number}.tiff"
        ref_path = os.path.join(ref_folder, ref_filename_expected)

        if os.path.exists(ref_path):
            file_pairs.append((pred_path, ref_path, base_name))
            logging.debug(f"找到匹配对: {pred_filename} <-> {os.path.basename(ref_path)}")
        else:
            logging.warning(f"未找到 '{pred_filename}' 对应的参考文件: {ref_filename_expected}，跳过此预测文件。")

    logging.info(f"成功匹配 {len(file_pairs)} 对文件。")
    return file_pairs

def process_image_pair(pred_path, ref_path, base_name, output_folder):
    """
    处理单个图像对：读取、验证/重采样、计算并保存分离性图。

    Args:
        pred_path (str): 预测掩膜文件路径。
        ref_path (str): 参考掩膜文件路径。
        base_name (str): 文件对的基础名称 (xxx)。
        output_folder (str): 保存分离性图的输出文件夹。

    Returns:
        dict: 包含处理结果的字典。
              成功时: {'success': True, 'true_labels': ndarray, 'pred_labels': ndarray}
              失败时: {'success': False, 'error': str}
    """
    logging.info(f"--- 开始处理图像对: {base_name} ---")
    try:
        with rasterio.open(pred_path) as pred_src, rasterio.open(ref_path) as ref_src:
            # 获取参考掩膜的形状、坐标系和地理变换信息 (目标属性)
            target_shape = ref_src.shape
            target_transform = ref_src.transform
            target_crs = ref_src.crs
            ref_mask_data = ref_src.read(1).astype(np.uint8)
            ref_profile = ref_src.profile # 获取参考文件的元数据用于写入分离性图


            # 获取预测掩膜的原始数据、形状、坐标系和地理变换信息
            pred_mask_orig_data = pred_src.read(1).astype(np.uint8)
            pred_orig_shape = pred_src.shape
            pred_orig_transform = pred_src.transform
            pred_orig_crs = pred_src.crs


            # 1. 检查形状和 CRS 是否匹配
            shape_mismatch = (pred_orig_shape != target_shape)
            crs_mismatch = (pred_orig_crs != target_crs) # rasterio CRS objects can be compared directly

            pred_mask_processed = None # 用于存储处理 (原始或重采样后) 的预测掩膜数据


            if shape_mismatch or crs_mismatch:
                logging.warning(f"   警告: 文件 {os.path.basename(pred_path)} 和 {os.path.basename(ref_path)} 存在形状 ({pred_orig_shape} vs {target_shape}) 或 CRS ({pred_orig_crs} vs {target_crs}) 不匹配。")
                logging.info(f"   正在将预测掩膜重采样到参考掩膜的形状和 CRS...")

                # 创建用于存放重采样结果的数组，形状和 dtype 与参考掩膜数据一致
                # 注意：这里 dtype 使用参考掩膜的 dtype (uint8)，这对于二值图是合适的
                pred_mask_processed = np.empty(target_shape, dtype=ref_mask_data.dtype)


                # 执行重采样
                reproject(
                    source=pred_mask_orig_data,        # 源数据 (预测掩膜原始数据)
                    destination=pred_mask_processed, # 目标数组 (用于存放重采样结果)
                    src_transform=pred_orig_transform,# 源地理变换
                    src_crs=pred_orig_crs,           # 源 CRS
                    dst_transform=target_transform,  # 目标地理变换 (参考掩膜的)
                    dst_crs=target_crs,            # 目标 CRS (参考掩膜的)
                    resampling=Resampling.nearest,  # 最近邻重采样方法
                    # 可以选择传递 src_nodata 和 dst_nodata 如果需要处理 NoData
                    src_nodata=pred_src.nodata,
                    dst_nodata=ref_src.nodata # 使用参考掩膜的 NoData 值作为目标 NoData
                )
                logging.info(f"   重采样完成。预测掩膜现在形状为 {pred_mask_processed.shape}，CRS 为 {target_crs}.")


            else:
                # 如果形状和 CRS 都匹配，则直接使用原始预测数据
                logging.info("   形状和 CRS 匹配，无需重采样。")
                pred_mask_processed = pred_mask_orig_data
                # 确保此时 pred_mask_processed 的形状确实与参考匹配（即使之前检查过，再确认一次）
                if pred_mask_processed.shape != target_shape:
                    logging.error(f"   致命错误: 在无需重采样的情况下，预测掩膜 ({pred_mask_processed.shape}) 仍与参考掩膜 ({target_shape}) 形状不匹配!")
                    return {'success': False, 'error': '内部逻辑错误：形状意外不匹配'}


            # 2. 验证掩膜值 (仅包含0和1) 和处理 NoData 值
            # 找到 NoData 像素并将其从评估中排除
            # 假设参考掩膜中的 NoData 区域不参与评估
            # 使用重采样后的 pred_mask_processed 和原始的 ref_mask_data
            valid_pixels_mask = (ref_mask_data != ref_src.nodata) if ref_src.nodata is not None else np.ones_like(ref_mask_data, dtype=bool)

            # 对于重采样后的预测掩膜，我们假设 NoData 是在重采样时根据参考掩膜设置的dst_nodata
            # 或者，如果重采样源有 NoData 且 dst_nodata 匹配，也会排除。
            # 最安全的做法是同时考虑参考和预测掩膜中被标记为 NoData 的像素
            if ref_src.nodata is not None: # 使用参考的 nodata 值检查重采样后的预测掩膜
                valid_pixels_mask = valid_pixels_mask & (pred_mask_processed != ref_src.nodata) # 使用参考的 NoData 值作为判断依据


            # 简单的检查，如果需要严格验证，需要处理 NoData 像素
            unique_pred = np.unique(pred_mask_processed[valid_pixels_mask]) # 只检查有效像素
            unique_ref = np.unique(ref_mask_data[valid_pixels_mask]) # 只检查有效像素
            valid_values = {0, 1}

            if not set(unique_pred).issubset(valid_values):
                 logging.warning(f"   警告: 预测掩膜 {os.path.basename(pred_path)} (处理后) 的有效像元包含非预期值: {unique_pred}。请确保它是二分类掩膜。")
            if not set(unique_ref).issubset(valid_values):
                 logging.warning(f"   警告: 参考掩膜 {os.path.basename(ref_path)} 的有效像元包含非预期值: {unique_ref}。请确保它是二分类掩膜。")


            # 如果处理后没有有效像素，则跳过
            if np.sum(valid_pixels_mask) == 0:
                 logging.warning(f"   警告: 图像对 {base_name} 处理后没有有效像素，跳过评估。")
                 return {'success': False, 'error': '无有效像素'}

            # 展平并仅保留有效像素
            true_labels_flat = ref_mask_data[valid_pixels_mask].flatten()
            pred_labels_flat = pred_mask_processed[valid_pixels_mask].flatten()


            # 3. 计算并保存分离性图 (Separability Map) - 在所有像素上计算，然后设置 NoData
            logging.info(f"   正在生成分离性图: {SEP_MAP_PREFIX}{base_name}{SEP_MAP_SUFFIX}")
            # 分离性图基于处理后的 pred_mask_processed 和 ref_mask_data
            sep_map = np.full(pred_mask_processed.shape, SEP_NODATA, dtype=np.int8) # 初始化为 NoData

            # 根据新的标签定义 (0=Normal, 1=Anomaly) 确定 TN, FP, FN, TP 区域
            # 在所有像素上计算，包括潜在的 NoData 区域（这些区域之后会被设置为 SEP_NODATA）
            tn_mask_all = (pred_mask_processed == 0) & (ref_mask_data == 0) # True Negative: Actual Normal, Pred Normal (Value 0)
            fp_mask_all = (pred_mask_processed == 1) & (ref_mask_data == 0) # False Positive: Actual Normal, Pred Anomaly (Value 1)
            fn_mask_all = (pred_mask_processed == 0) & (ref_mask_data == 1) # False Negative: Actual Anomaly, Pred Normal (Value 2)
            tp_mask_all = (pred_mask_processed == 1) & (ref_mask_data == 1) # True Positive: Actual Anomaly, Pred Anomaly (Value 3)

            sep_map[tn_mask_all] = 0 # TN - Blue
            sep_map[fp_mask_all] = 1 # FP - Red
            sep_map[fn_mask_all] = 2 # FN - Yellow
            sep_map[tp_mask_all] = 3 # TP - Green

            # 确保 NoData 区域在分离性图中显示为 NoData
            sep_map[~valid_pixels_mask] = SEP_NODATA

            # 更新 profile 以写入分离性图 (int8, 1 band, nodata value)
            # 使用参考掩膜的 profile，但更新 dtype 和 nodata
            ref_profile.update(dtype=rasterio.int8, count=1, nodata=SEP_NODATA)

            sep_map_filename = f"{SEP_MAP_PREFIX}{base_name}{SEP_MAP_SUFFIX}"
            sep_map_path = os.path.join(output_folder, sep_map_filename)

            with rasterio.open(sep_map_path, 'w', **ref_profile) as dst:
                 dst.write(sep_map, 1) # 直接写入 int8 类型
            logging.info(f"     分离性图已保存至: {sep_map_path}")

            # 4. 准备返回数据
            # 返回的是去除 NoData 后的展平数据
            return {
                'success': True,
                'true_labels': true_labels_flat,
                'pred_labels': pred_labels_flat
            }

    except rasterio.RasterioIOError as e:
        logging.error(f"   错误: 读取文件 {os.path.basename(pred_path)} 或 {os.path.basename(ref_path)} 时出错: {e}")
        return {'success': False, 'error': f"Rasterio 读取错误: {e}"}
    except Exception as e:
        logging.error(f"   错误: 处理图像对 {base_name} 时发生未知错误: {e}")
        return {'success': False, 'error': f"未知处理错误: {e}"}


def calculate_overall_metrics(y_true_all, y_pred_all):
    """
    根据聚合的真实标签和预测标签计算总体评估指标。
    (标签含义: 0=Normal, 1=Anomaly)

    Args:
        y_true_all (ndarray): 所有图像对展平后的真实标签数组 (已去除 NoData)。
        y_pred_all (ndarray): 所有图像对展平后的预测标签数组 (已去除 NoData)。

    Returns:
        dict: 包含所有计算出的指标的字典。
              键包括 'cm'(confusion_matrix), 'iou_per_class', 'mIoU', 'f1_per_class',
              'f1_macro', 'f1_weighted', 'fpr', 'tpr', 'roc_auc', 'mAUC', 'kappa'。
    """
    logging.info("\n--- 开始计算总体评估指标 ---")
    metrics = {}
    labels_order = [0, 1] # Explicitly define order: Normal, Anomaly

    # 确保输入数据不为空，避免 sklearn 报错
    if y_true_all.size == 0 or y_pred_all.size == 0:
        logging.warning("输入数据为空，无法计算总体指标。")
        return {
            'cm': np.zeros((2, 2), dtype=int),
            'iou_per_class': [0.0, 0.0],
            'miou': 0.0,
            'f1_per_class': [0.0, 0.0],
            'f1_macro': 0.0,
            'f1_weighted': 0.0,
            'fpr': np.array([0.0, 1.0]), # Default ROC points
            'tpr': np.array([0.0, 1.0]), # Default ROC points
            'roc_auc': 0.5, # AUC of a random classifier
            'mAUC': 0.5,
            'kappa': 0.0 # Default kappa
        }


    # 1. 混淆矩阵 (labels=[0, 1] -> rows/cols are Normal, Anomaly)
    logging.info("计算混淆矩阵...")
    cm = confusion_matrix(y_true_all, y_pred_all, labels=labels_order)
    metrics['cm'] = cm
    # cm[0,0]=TN (True Normal), cm[0,1]=FP (Normal->Anomaly)
    # cm[1,0]=FN (Anomaly->Normal), cm[1,1]=TP (True Anomaly)

    # 2. IoU (Jaccard Index)
    logging.info("计算 IoU (Jaccard Index)...")
    iou_per_class = jaccard_score(y_true_all, y_pred_all, average=None, labels=labels_order, zero_division=0)
    miou = jaccard_score(y_true_all, y_pred_all, average='macro', labels=labels_order, zero_division=0)
    metrics['iou_per_class'] = iou_per_class # [IoU_Normal, IoU_Anomaly]
    metrics['miou'] = miou

    # 3. F1-Score
    logging.info("计算 F1-Score...")
    f1_per_class = f1_score(y_true_all, y_pred_all, average=None, labels=labels_order, zero_division=0)
    f1_macro = f1_score(y_true_all, y_pred_all, average='macro', labels=labels_order, zero_division=0)
    f1_weighted = f1_score(y_true_all, y_pred_all, average='weighted', labels=labels_order, zero_division=0)
    metrics['f1_per_class'] = f1_per_class # [F1_Normal, F1_Anomaly]
    metrics['f1_macro'] = f1_macro
    metrics['f1_weighted'] = f1_weighted

    # 4. ROC 曲线和 AUC
    # roc_curve assumes `pos_label=1` by default, which is now 'Anomaly'.
    # This calculates performance for detecting 'Anomaly'.
    # 注意: 对于二值(0/1)预测而非概率分数，roc_curve 的输出可能只有少数几个点 (0,0), (FPR, TPR), (1,1)
    logging.info("计算 ROC 曲线和 AUC (以 Anomaly 为正类)...")
    try:
        # Check if there are enough unique values for ROC calculation
        if len(np.unique(y_true_all)) > 1 and len(np.unique(y_pred_all)) > 1:
            fpr, tpr, _ = roc_curve(y_true_all, y_pred_all, pos_label=1) # pos_label=1 means Anomaly
            roc_auc = auc(fpr, tpr)
            metrics['fpr'] = fpr # 假阳性率值序列
            metrics['tpr'] = tpr # 真阳性率值序列
            metrics['roc_auc'] = roc_auc
            metrics['mAUC'] = roc_auc # Typically refers to the overall AUC for the positive class
        else:
             logging.warning("ROC/AUC 计算需要真实标签和预测标签都有多个唯一值。跳过计算。")
             # Assign default values indicating skipped calculation
             metrics['fpr'] = np.array([0.0, 1.0])
             metrics['tpr'] = np.array([0.0, 1.0])
             metrics['roc_auc'] = 0.5
             metrics['mAUC'] = 0.5


    except ValueError as e:
        logging.warning(f"计算 ROC/AUC 时发生错误 (可能输入值不足或类别分布异常): {e}")
        metrics['fpr'] = np.array([0.0, 1.0])
        metrics['tpr'] = np.array([0.0, 1.0])
        metrics['roc_auc'] = 0.5
        metrics['mAUC'] = 0.5


    # 5. 卡帕系数 (Cohen's Kappa)
    logging.info("计算卡帕系数...")
    # weight=None 计算的是线性 Kappa 或二次 Kappa，对于分类，通常使用 None (等权重) 或 'linear'/'quadratic'
    # 对于二分类，直接计算即可
    kappa = cohen_kappa_score(y_true_all, y_pred_all, labels=labels_order)
    metrics['kappa'] = kappa


    logging.info("总体指标计算完成。")
    return metrics

def log_formulas():
    """将指标计算公式记录到日志 (标签含义: 0=Normal, 1=Anomaly)。"""
    logging.info("\n--- 指标计算公式 ---")
    logging.info(r"标签: 0 -> Normal (负类/背景), 1 -> Anomaly (正类/目标)")
    logging.info(r"以下 TP, TN, FP, FN 是基于将 'Anomaly' (1) 视为正类来定义的:")
    logging.info(r"TP (True Positive): 正确预测为 Anomaly 的像元数 (Actual=1, Predicted=1)")
    logging.info(r"TN (True Negative): 正确预测为 Normal 的像元数 (Actual=0, Predicted=0)")
    logging.info(r"FP (False Positive): 错误预测为 Anomaly 的像元数 (Actual=0, Predicted=1) - Type I Error / False Alarm")
    logging.info(r"FN (False Negative): 错误预测为 Anomaly 的像元数 (Actual=1, Predicted=0) - Type II Error / Miss")
    logging.info("")
    logging.info(r"Confusion Matrix (sklearn, labels=[0, 1]):")
    logging.info(r"      Predicted:")
    logging.info(r"      Normal(0) Anomaly(1)")
    logging.info(r"Actual")
    logging.info(r"Normal(0) [[TN,        FP],")
    logging.info(r"Anomaly(1) [ FN,        TP]]")
    logging.info("")
    logging.info(r"IoU (Intersection over Union / Jaccard Index) per class:")
    logging.info(r"  IoU = Intersection / Union")
    logging.info(r"  - IoU (Anomaly) = TP / (TP + FP + FN)")
    logging.info(r"  - IoU (Normal)  = TN / (TN + FP + FN)") # Calculated using values relative to Normal class
    logging.info(r"mIoU (Mean IoU) = (IoU_Normal + IoU_Anomaly) / 2")
    logging.info("")
    logging.info(r"F1-Score per class (based on Anomaly=positive):")
    logging.info(r"  Precision (Anomaly) = TP / (TP + FP)")
    logging.info(r"  Recall (Sensitivity/TPR, Anomaly) = TP / (TP + FN)")
    logging.info(r"  F1 (Anomaly) = 2 * (Precision_Anomaly * Recall_Anomaly) / (Precision_Anomaly + Recall_Anomaly)")
    logging.info(r"  F1 (Normal): Calculated similarly, treating Normal as the positive class.")
    logging.info(r"      Precision_Normal = TN / (TN + FP)") # Correct calculation for Normal as positive
    logging.info(r"      Recall_Normal = TN / (TN + FN)")
    logging.info(r"      F1_Normal = 2 * (Precision_Normal * Recall_Normal) / (Precision_Normal + Recall_Normal)")
    logging.info(r"Macro F1-Score = (F1_Normal + F1_Anomaly) / 2")
    logging.info("")
    logging.info(r"ROC Curve (Receiver Operating Characteristic, based on Anomaly=positive):")
    logging.info(r"  Plots True Positive Rate (TPR) vs. False Positive Rate (FPR)")
    logging.info(r"  TPR (Recall/Sensitivity, for Anomaly) = TP / (TP + FN)")
    logging.info(r"  FPR (False Alarm Rate, for Anomaly) = FP / (FP + TN)")
    logging.info(r"AUC (Area Under Curve): ROC 曲线下的面积。")
    logging.info(r"mAUC: 在二分类中通常指的就是基于正类 (Anomaly) 的整体 AUC。")
    logging.info("(注意: 对于二值(0/1)预测而非概率分数，ROC/AUC 的解释可能受限，曲线可能退化)")
    logging.info("")
    # 添加卡帕系数公式
    logging.info(r"Kappa Coefficient (Cohen's Kappa):")
    logging.info(r"  Measures agreement accounting for chance.")
    logging.info(r"  Kappa = (Po - Pe) / (1 - Pe)")
    logging.info(r"  Po (Observed Agreement) = (TP + TN) / Total Pixels")
    logging.info(r"  Pe (Expected Agreement by Chance) = ((TP + FP)*(TP + FN) + (FN + TN)*(FP + TN)) / (Total Pixels)^2") # Corrected Pe formula based on marginals
    logging.info(r"  (Calculated by sklearn.metrics.cohen_kappa_score)")


def log_results(metrics, total_pixels, label_map):
    """将计算出的指标结果格式化并记录到日志 (标签含义: 0=Normal, 1=Anomaly)。"""
    logging.info("\n--- 总体评估结果 ---")
    logging.info(f"处理的总像元数: {total_pixels}")

    # 混淆矩阵 (labels=[0, 1] -> rows/cols are Normal, Anomaly)
    cm = metrics['cm']
    # 检查混淆矩阵是否为空或异常
    if cm.shape != (2, 2):
         logging.error("计算得到非预期的混淆矩阵形状，无法提取 TP/TN/FP/FN。")
         logging.info(f"Confusion Matrix:\n{cm}")
         # Log other metrics as 0.0 or default
         return

    # 使用 .ravel() 确保正确获取 TP, TN, FP, FN，特别是当输入数据规模较小时
    # 注意 unravel 的顺序是 TN, FP, FN, TP
    tn, fp, fn, tp = cm.ravel()

    logging.info("\nConfusion Matrix:")
    logging.info(f"             | Predicted: {label_map[0]:<10} | Predicted: {label_map[1]:<10}")
    logging.info("-" * 50)
    logging.info(f"Actual: {label_map[0]:<10} | {tn:12d} (TN) | {fp:12d} (FP)")
    logging.info(f"Actual: {label_map[1]:<10} | {fn:12d} (FN) | {tp:12d} (TP)")
    logging.info(f"  (TN: Correct Normal, FP: Normal as Anomaly, FN: Anomaly as Normal, TP: Correct Anomaly)")


    # 卡帕系数
    # Check if kappa was calculated successfully
    if 'kappa' in metrics:
        kappa = metrics['kappa']
        logging.info(f"\nKappa Coefficient:")
        logging.info(f"Kappa Coefficient: {kappa:.4f}")
    else:
         logging.warning("\nKappa Coefficient Calculation Skipped.")


    # IoU (iou_per_class[0] is for Normal, iou_per_class[1] is for Anomaly)
    iou_per_class = metrics['iou_per_class']
    miou = metrics['miou']
    logging.info("\nIoU (Intersection over Union / Jaccard Index):")
    logging.info(f"IoU ({label_map[0]}): {iou_per_class[0]:.4f}") # Index 0 corresponds to label 0 (Normal)
    logging.info(f"IoU ({label_map[1]}): {iou_per_class[1]:.4f}") # Index 1 corresponds to label 1 (Anomaly)
    logging.info(f"mIoU (Mean IoU): {miou:.4f}")

    # F1-Score (f1_per_class[0] is for Normal, f1_per_class[1] is for Anomaly)
    f1_per_class = metrics['f1_per_class']
    f1_macro = metrics['f1_macro']
    f1_weighted = metrics['f1_weighted']
    logging.info("\nF1-Score:")
    logging.info(f"F1-Score ({label_map[0]}): {f1_per_class[0]:.4f}") # Index 0 corresponds to label 0 (Normal)
    logging.info(f"F1-Score ({label_map[1]}): {f1_per_class[1]:.4f}") # Index 1 corresponds to label 1 (Anomaly)
    logging.info(f"Macro F1-Score: {f1_macro:.4f}")
    logging.info(f"Weighted F1-Score: {f1_weighted:.4f}")

    # AUC (Calculated with Anomaly=1 as positive class)
    # Check if AUC was calculated successfully
    if 'roc_auc' in metrics:
         roc_auc = metrics['roc_auc']
         mAUC = metrics['mAUC']
         logging.info("\nAUC (Area Under ROC Curve):")
         logging.info(f"AUC (Anomaly as positive): {roc_auc:.4f}")
         logging.info(f"mAUC: {mAUC:.4f}")
    else:
         logging.warning("\nAUC Calculation Skipped due to error or insufficient data.")


def plot_roc_curve(fpr, tpr, roc_auc, output_filepath):
    """
    绘制并保存 ROC 曲线图 (基于 Anomaly=1 为正类计算的 FPR/TPR)。

    Args:
        fpr (ndarray): False Positive Rate 数组。
        tpr (ndarray): True Positive Rate 数组。
        roc_auc (float): 计算出的 AUC 值。
        output_filepath (str): 保存 ROC 曲线图的文件路径。
    """
    # 只有当 fpr 和 tpr 包含有效数据时才绘制
    if fpr is not None and tpr is not None and len(fpr) > 1 and len(tpr) > 1:
        logging.info(f"\n绘制并保存 ROC 曲线到: {output_filepath}")
        plt.figure(figsize=(8, 6))
        plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC curve (Anomaly Detection, area = {roc_auc:.4f})')
        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--') # 对角线
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate (FPR = FP / (FP + TN)) - Rate of Normals classified as Anomaly')
        plt.ylabel('True Positive Rate (TPR = TP / (TP + FN)) - Rate of Anomalies correctly classified')
        plt.title('Receiver Operating Characteristic (ROC) Curve (Anomaly vs Normal)')
        plt.legend(loc="lower right")
        plt.grid(True)
        try:
            plt.savefig(output_filepath)
            logging.info(f"   ROC 曲线图已成功保存。")
        except Exception as e:
            logging.error(f"   保存 ROC 曲线图失败: {e}")
        finally:
            plt.close() # 关闭图形，释放内存
    else:
        logging.warning("ROC 曲线数据无效或不足，跳过绘图。")


def log_separability_legend(sep_colors, sep_labels):
    """记录分离性图的图例说明到日志 (标签含义: 0=Normal, 1=Anomaly)。"""
    logging.info("\n--- Separability Map Legend ---")
    logging.info(f"分离性图 (e.g., {SEP_MAP_PREFIX}xxx{SEP_MAP_SUFFIX}) 可视化每个像素的分类:")
    # SEP_LABELS has already been updated
    for i, label in enumerate(sep_labels):
        logging.info(f"  Value {i} ({sep_colors[i]}): {label}")

# --- 主程序 ---
def main():
    """主执行函数"""
    start_time = datetime.now()
    log_filepath = setup_environment(OUTPUT_FOLDER, LOG_FILE_NAME)

    logging.info(f"评价脚本开始运行: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"标签定义: {LABEL_MAP}")
    logging.info(f"预测掩膜文件夹: {PRED_FOLDER}")
    logging.info(f"参考掩膜文件夹: {REF_FOLDER}")
    logging.info(f"结果输出文件夹: {OUTPUT_FOLDER}")

    # 1. 查找文件对
    file_pairs = find_file_pairs(PRED_FOLDER, REF_FOLDER)
    if not file_pairs:
        logging.error("未能找到任何有效的图像对。请检查输入文件夹和文件命名。脚本将退出。")
        sys.exit(1) # 退出脚本

    # 2. 处理每个图像对并收集数据
    all_true_labels = []
    all_pred_labels = []
    processed_pairs_count = 0
    failed_pairs_count = 0

    for pred_path, ref_path, base_name in file_pairs:
        result = process_image_pair(pred_path, ref_path, base_name, OUTPUT_FOLDER)
        if result['success']:
            all_true_labels.append(result['true_labels'])
            all_pred_labels.append(result['pred_labels'])
            processed_pairs_count += 1
        else:
            # process_image_pair 已经在内部记录了具体的错误
            failed_pairs_count += 1

    logging.info(f"\n--- 图像处理总结 ---")
    logging.info(f"成功处理的图像对数量: {processed_pairs_count}")
    logging.info(f"处理失败的图像对数量: {failed_pairs_count}")

    if processed_pairs_count == 0:
        logging.error("没有成功处理任何图像对，无法计算总体指标。脚本将退出。")
        sys.exit(1) # 退出脚本

    # 3. 聚合数据
    logging.info("聚合所有成功处理的图像对的数据...")
    y_true_all = np.concatenate(all_true_labels)
    y_pred_all = np.concatenate(all_pred_labels)
    total_pixels = y_true_all.size

    # 4. 计算总体指标
    metrics = calculate_overall_metrics(y_true_all, y_pred_all)

    # 5. 记录公式和结果
    log_formulas()
    log_results(metrics, total_pixels, LABEL_MAP)

    # 6. 绘制并保存 ROC 曲线
    # 检查 metrics 字典中是否存在 roc_auc 和 fpr/tpr，因为 calculate_overall_metrics 在数据不足时可能跳过计算
    if 'roc_auc' in metrics and metrics['fpr'] is not None and metrics['tpr'] is not None:
        roc_curve_filepath = os.path.join(OUTPUT_FOLDER, ROC_CURVE_FILE_NAME)
        plot_roc_curve(metrics['fpr'], metrics['tpr'], metrics['roc_auc'], roc_curve_filepath)
    else:
        logging.warning("未找到有效的 ROC 曲线数据，跳过 ROC 曲线图绘制。")


    # 7. 记录分离性图图例
    log_separability_legend(SEP_COLORS, SEP_LABELS)

    # 8. 结束信息
    end_time = datetime.now()
    logging.info(f"\n评价脚本运行结束: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"总耗时: {end_time - start_time}")
    logging.info(f"所有结果已记录在: {log_filepath}")
    if 'roc_auc' in metrics and metrics['fpr'] is not None and metrics['tpr'] is not None:
         logging.info(f"ROC 曲线图保存在: {roc_curve_filepath}")
    logging.info(f"分离性图 (GeoTIFF) 保存在 '{OUTPUT_FOLDER}' 文件夹中，文件名为 {SEP_MAP_PREFIX}*{SEP_MAP_SUFFIX}")


if __name__ == '__main__':
    main()