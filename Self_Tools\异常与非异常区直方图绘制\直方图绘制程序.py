import os
import rasterio
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm # 导入字体管理器
from typing import Union

# --- 设置文件夹路径 ---
data_folder = 'S2A_Loss'  # 灾后数据文件夹
mask_folder = 'Mask'
output_folder = 'Histograms' # 用于保存直方图的输出文件夹

# 查找数字函数
def extract_number_re(filename: str) -> Union[int, None]:
    """
    使用正则表达式从文件名中提取第一个连续的数字序列。

    参数:
        filename: 待处理的文件名字符串。

    返回:
        提取到的整数数字，如果没有找到则返回None。
    """
    import re
    # \d+ 匹配一个或多个数字
    # re.search 从字符串的开头扫描，查找第一个匹配项
    match = re.search(r'\d+', filename)
    if match:
        return int(match.group(0))  # group(0) 返回整个匹配到的字符串
    return None

# --- 配置 Matplotlib 字体以支持中文 ---
# 尝试查找系统中可用的中文字体
def find_chinese_font():
    # 常见的中文字体名称列表
    font_names = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'FangSong', 'PingFang SC', 'Hiragino Sans GB', 'WenQuanYi Zen Hei', 'Arial Unicode MS']
    for font_name in font_names:
        try:
            # 查找字体文件路径
            font_path = fm.findfont(font_name)
            print(f"找到中文字体: {font_name}")
            return font_name
        except:
            continue
    print("未找到常见中文字体，请手动安装或指定字体路径。尝试使用默认字体，可能显示为方块。")
    return None

chinese_font = find_chinese_font()

if chinese_font:
    # 设置全局字体
    plt.rcParams['font.family'] = chinese_font
    # 解决负号'-'显示为方块的问题
    plt.rcParams['axes.unicode_minus'] = False
else:
    # 如果没有找到中文字体，你可以选择指定一个绝对路径的字体文件
    # 例如：font_path = '/path/to/your/font.ttf'
    # if os.path.exists(font_path):
    #     plt.rcParams['font.family'] = fm.FontProperties(fname=font_path).get_name()
    #     plt.rcParams['axes.unicode_minus'] = False
    #     print(f"使用指定字体文件: {font_path}")
    # else:
    #     print("指定的字体文件不存在。")
    pass # 继续使用默认字体，可能会显示为方块


# --- 确保输出文件夹存在 ---
os.makedirs(output_folder, exist_ok=True)

# --- 获取数据文件列表并提取主文件名 ---
data_files = [f for f in os.listdir(data_folder) if f.endswith('.tiff')]

print(f"找到 {len(data_files)} 个误差图文件。")

# --- 遍历每个数据文件，处理对应的场景 ---
for data_file in data_files:
    # 提取主文件名 (例如，从 'SceneID_Average*.tiff' 中提取 'SceneID')
    if data_file.endswith('.tiff'):
        main_filename = data_file.replace('_Average*.tiff', '')
        SceneID = extract_number_re(main_filename) # 提取其中的数字
    else:
        print(f"跳过非预期的文件名格式: {data_file}")
        continue

    print(f"\n正在处理场景: {main_filename}")

    # 构建对应的数据和掩膜文件的完整路径
    data_file_path = os.path.join(data_folder, data_file)
    mask_file_path = os.path.join(mask_folder, f"mask_{SceneID}.tiff")

    # --- 检查所有必需的文件是否存在 ---
    if not os.path.exists(data_file_path):
        print(f"错误: 找不到对应的误差图文件: {data_file_path} (这不应该发生，除非列表生成错误)")
        continue
    if not os.path.exists(mask_file_path):
        print(f"错误: 找不到对应的掩膜文件: {mask_file_path}")
        continue

    # --- 读取遥感影像数据 ---
    try:
        with rasterio.open(data_file_path) as src_data, \
             rasterio.open(mask_file_path) as src_mask:

            # 确保所有影像的尺寸一致
            if src_data.shape != src_mask.shape:
                print(f"警告: 场景 {main_filename} 的影像尺寸不一致。跳过。")
                continue

            # 读取数据到numpy数组
            # masked=True 会自动处理NoData值
            data = src_data.read(1, masked=True)
            mask_data = src_mask.read(1) # 掩膜通常没有NoData，如果你的有，请调整

    except rasterio.errors.RasterioIOError as e:
        print(f"错误: 无法读取场景 {main_filename} 的一个或多个遥感影像文件: {e}")
        continue
    except Exception as e:
        print(f"处理场景 {main_filename} 时发生未知错误: {e}")
        continue

    # --- 1. 提取异常区和非异常区数据 ---
    # 找到掩膜值为1且原始loss数据有效的像元索引（异常区）
    anomaly_indices = (mask_data == 1) & (~data.mask)
    anomaly_values = data[anomaly_indices]

    # 找到掩膜值为0且原始loss数据有效的像元索引（非异常区）
    normal_indices = (mask_data == 0) & (~data.mask)
    normal_values = data[normal_indices]

    # --- 2. 绘制异常区与非异常区对比直方图 ---
    print("绘制异常区与非异常区对比直方图...")

    if anomaly_values.size == 0 and normal_values.size == 0:
        print("警告: 没有有效数据，跳过直方图绘制。")
        continue

    plt.figure(figsize=(10, 6))
    
    # 绘制异常区和非异常区的直方图
    if anomaly_values.size > 0:
        plt.hist(anomaly_values, bins=100, color='darkred', histtype='step', density=True, label='异常区')
    
    if normal_values.size > 0:
        plt.hist(normal_values, bins=100, color='darkblue', histtype='step', density=True, label='非异常区')

    plt.title(f'场景 {main_filename} 异常区与非异常区的 Loss 直方图对比')
    plt.xlabel('Loss 值')
    plt.ylabel('概率密度')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.6)

    # 保存直方图
    output_path = os.path.join(output_folder, f"{main_filename}_Anomaly_vs_Normal_Histograms.png")
    plt.savefig(output_path)
    print(f"已保存异常区与非异常区对比直方图到: {output_path}")
    plt.close()

    # # --- 3. 单独绘制异常区直方图 ---
    # if anomaly_values.size > 0:
    #     plt.figure(figsize=(10, 6))
    #     plt.hist(anomaly_values, bins=100, color='darkred', histtype='step', density=True)
    #     plt.title(f'场景 {main_filename} 异常区的 Loss 直方图')
    #     plt.xlabel('Loss 值')
    #     plt.ylabel('概率密度')
    #     plt.grid(True, linestyle='--', alpha=0.6)
        
    #     output_path_anomaly = os.path.join(output_folder, f"{main_filename}_Anomaly_Histograms.png")
    #     plt.savefig(output_path_anomaly)
    #     print(f"已保存异常区直方图到: {output_path_anomaly}")
    #     plt.close()
    # else:
    #     print("警告: 异常区中没有有效数据，跳过异常区直方图绘制。")

    # # --- 4. 单独绘制非异常区直方图 ---
    # if normal_values.size > 0:
    #     plt.figure(figsize=(10, 6))
    #     plt.hist(normal_values, bins=100, color='darkblue', histtype='step', density=True)
    #     plt.title(f'场景 {main_filename} 非异常区的 Loss 直方图')
    #     plt.xlabel('Loss 值')
    #     plt.ylabel('概率密度')
    #     plt.grid(True, linestyle='--', alpha=0.6)
        
    #     output_path_normal = os.path.join(output_folder, f"{main_filename}_Normal_Histograms.png")
    #     plt.savefig(output_path_normal)
    #     print(f"已保存非异常区直方图到: {output_path_normal}")
    #     plt.close()
    # else:
    #     print("警告: 非异常区中没有有效数据，跳过非异常区直方图绘制。")

print("\n所有场景处理完毕。")