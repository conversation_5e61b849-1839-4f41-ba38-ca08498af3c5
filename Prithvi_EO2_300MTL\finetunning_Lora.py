####### 对decoder层的lora微调
# 导入基础处理库
import os
import numpy as np
import datetime
import yaml,json,logging,sys
import matplotlib.pyplot as plt
from tqdm import tqdm
# 地理数据处理有关库
from osgeo import gdal
import rasterio
# torch家族
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms as T
# 图像增强库
os.environ['ALBUMENTATIONS_SUPPRESS_CHECK'] = '1' # 或者尝试 'True'
import albumentations, albumentations.pytorch
# 导入模型创建库
from models import PrithviMAE
# 导入 PEFT 库 (微调用)
from peft import get_peft_model, LoraConfig, TaskType, PeftModel
import shutil

# --- Logging Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- 函数文件 ---

# tif影像加载
def read_geotiff(file_path:str, bands:list): # 从 *file_path* 读取所有波段并返回图像+元数据信息
    with rasterio.open(file_path) as src: # 使用 rasterio 打开 GeoTIFF 文件
        img = src.read(bands) # 读取所有波段的图像数据
        meta = src.meta # 获取 GeoTIFF 文件的元数据
        try:
            coords = src.lnglat() # 尝试获取图像的中心经纬度坐标
        except:
            # 无法读取坐标
            coords = None # 如果读取失败，将坐标设为 None
    return img,meta,coords # 返回图像数据(np.ndarray,形状c、h、w)，元数据，坐标

# LoRA层注入
def inject_lora_to_model(model, lora_config_dict):
    """
    将 LoRA 层注入到给定的模型中。
    
    Args:
        model (torch.nn.Module): 要注入 LoRA 的基座模型。
        lora_config_dict (dict): 包含 LoRA 配置参数的字典。
    
    Returns:
        torch.nn.Module: 注入 LoRA 后的模型 (peft.PeftModel)。
    """
    logging.info("开始注入 LoRA 层...")

    # 冻结编码器参数 peft.get_peft_model 默认会冻结基座模型的参数，所以我们这里可以不手动冻结
    # 但为了明确意图，可以加一个循环来冻结编码器
    if hasattr(model, 'encoder'):
        for param in model.encoder.parameters():
            param.requires_grad = False
        print("Encoder parameters frozen.")
    
    # 实例化 LoraConfig
    lora_config = LoraConfig(
        r=lora_config_dict.get("lora_rank"), # 低秩的维度
        lora_alpha=lora_config_dict.get("lora_alpha"), # 低秩矩阵的缩放因子
        target_modules=lora_config_dict.get("target_modules"),  # 添加至的位置
        lora_dropout=lora_config_dict.get("lora_dropout"), # 应用于 LoRA 适配器中矩阵 A 的 Dropout 概率
        bias="none", # 是否对偏置 (bias) 项微调
        task_type=TaskType.FEATURE_EXTRACTION, # 根据你的任务类型选择
    )
    
    # 对解码器应用 LoRA (使用 peft 库包装模型，注入 LoRA 层)
    peft_model = get_peft_model(model, lora_config)
    logging.info(f"LoRA applied to MAEDecoder with rank={lora_config.r}, alpha={lora_config.lora_alpha}.")
    peft_model.print_trainable_parameters() # 打印可训练参数量

    return peft_model

# 模型加载函数
def load_model(config_path, out_config_path, Lora_config_dict, checkpoint_path):
    # 执行设备判断
    device = "cuda" if torch.cuda.is_available() else "cpu"
    logging.info(f'Current using device: {device}')

    with open(config_path, "r") as f: # 载入配置文件
        config = yaml.safe_load(f)['pretrained_cfg'] # 从配置文件加载原始配置

    # 1. 实例化原始模型 (不带 LoRA 的)
    model = PrithviMAE(**config) # 虽然里面包含lora参数，但init中不包含那些参数的初始化
    logging.info("Foundation Model————PrithviMAE————has been created.")

    # 2. 加载预训练权重
    logging.info(f"正在从 {checkpoint_path} 加载预训练权重...")
    state_dict = torch.load(checkpoint_path, map_location=device) # 读取预训练模型权重

    # 处理位置编码尺寸不匹配的问题（从4时序到1时序）
    current_state_dict = model.state_dict() # 获取当前模型的初始化权重字典

    # 检查并调整编码器位置编码
    if 'encoder.pos_embed' in state_dict and 'encoder.pos_embed' in current_state_dict:
        pretrained_pos_embed = state_dict['encoder.pos_embed']  # [1, 785, 1024] (4时序) 
        # [B, N, D],批次维,序列长度,嵌入向量维度 
        # 序列长度197 = 图像块的数量196(16*16) + 特殊的分类Token1（[CLS] Token）
        current_pos_embed = current_state_dict['encoder.pos_embed']  # [1, 197, 1024] (1时序)

        if pretrained_pos_embed.shape != current_pos_embed.shape:
            logging.info(f"调整编码器位置编码: {pretrained_pos_embed.shape} -> {current_pos_embed.shape}")
            # 保留CLS token的位置编码 [1, 1, 1024]
            cls_pos_embed = pretrained_pos_embed[:, :1, :]
            # 取第一个时间帧的位置编码 [1, 196, 1024]
            spatial_pos_embed = pretrained_pos_embed[:, 1:197, :]
            # 组合新的位置编码
            new_pos_embed = torch.cat([cls_pos_embed, spatial_pos_embed], dim=1)
            state_dict['encoder.pos_embed'] = new_pos_embed

    # 检查并调整解码器位置编码
    if 'decoder.decoder_pos_embed' in state_dict and 'decoder.decoder_pos_embed' in current_state_dict:
        pretrained_decoder_pos_embed = state_dict['decoder.decoder_pos_embed']  # [1, 785, 512]
        current_decoder_pos_embed = current_state_dict['decoder.decoder_pos_embed']  # [1, 197, 512]

        if pretrained_decoder_pos_embed.shape != current_decoder_pos_embed.shape:
            logging.info(f"调整解码器位置编码: {pretrained_decoder_pos_embed.shape} -> {current_decoder_pos_embed.shape}")
            # 保留CLS token的位置编码 [1, 1, 512]
            cls_decoder_pos_embed = pretrained_decoder_pos_embed[:, :1, :]
            # 取第一个时间帧的位置编码 [1, 196, 512]
            spatial_decoder_pos_embed = pretrained_decoder_pos_embed[:, 1:197, :]
            # 组合新的位置编码
            new_decoder_pos_embed = torch.cat([cls_decoder_pos_embed, spatial_decoder_pos_embed], dim=1)
            state_dict['decoder.decoder_pos_embed'] = new_decoder_pos_embed

    model.load_state_dict(state_dict, strict=True) # 加载模型参数
    logging.info(f"Successfully Loaded checkpoint from {checkpoint_path}") # 打印加载成功的消息

    # 3. 如果需要启用 LoRA 微调，则在加载完原始权重后调用注入函数
    if Lora_config_dict.get("use_lora", False):
        # 调用Lora层注入函数，在现有模型基础上加入微调层
        model = inject_lora_to_model(model, Lora_config_dict)
        
        # 将 LoRA 相关的配置更新到 config 中，然后保存
        config.update(Lora_config_dict)
        config["device"] = device
        os.makedirs(os.path.dirname(out_config_path), exist_ok=True)
        with open(out_config_path, 'w') as f:
            yaml.safe_dump({'pretrained_cfg': config}, f)
        logging.info(f"LoRA 配置已启用并保存到 {out_config_path}")
        
    else: # 不启用 LoRA 微调的情况下，保存原始配置
        config["device"] = device
        os.makedirs(os.path.dirname(out_config_path), exist_ok=True)
        with open(out_config_path, 'w') as f:
            yaml.safe_dump({'pretrained_cfg': config}, f)
        logging.info(f"LoRA 配置已禁用。原始配置已保存到 {out_config_path}")

    total_params = sum(p.numel() for p in model.parameters()) # 计算总体模型参数
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad) # 计算被训练参数
    print(f"可被微调的模型参数量: {trainable_params}") # 打印参数
    logging.info(f"\n--> Total Model parameters: {total_params:,}") # 参数记录
    logging.info(f"--> Trainable Model parameters: {trainable_params:,}\n")  # 参数记录

    model.to(device) # 将模型移动到指定设备

    return config, model, device

# 训练函数
def train_epoch(model, dataloader, optimizer, device, mask_ratio=0.75):
    model.train(True)
    total_loss = 0
    # 逐batch处理数据
    with tqdm(dataloader, total=len(dataloader), desc="Training") as pbar:
        for batch_idx, batch in enumerate(pbar):
            # 获取数据
            images = batch["image"].to(device) # 此时images的形状为 B、C、H、W
            optimizer.zero_grad() # 清空梯度
                  
            # 处理时间坐标：对于单时序输入，需要确保维度为 (B, T=1, 2)
            if "temporal_coords" in batch and batch["temporal_coords"] is not None:
                temporal_coords = batch["temporal_coords"]
                # DataLoader会将列表转换为张量列表，需要重新组合
                if isinstance(temporal_coords, list) and len(temporal_coords) == 2:
                    # temporal_coords是[tensor([year1, year2, ...]), tensor([day1, day2, ...])]的形式
                    temporal_coords = torch.stack(temporal_coords, dim=1).float()  # (B, 2)
                elif not isinstance(temporal_coords, torch.Tensor): # 如果不是张量
                    temporal_coords = torch.tensor(temporal_coords, dtype=torch.float32)
                else:
                    temporal_coords = temporal_coords.float()
                temporal_coords = temporal_coords.unsqueeze(1).to(device)  # (B, 2) -> (B, 1, 2)
            else:
                temporal_coords = None

            # 处理位置坐标：维度为 (B, 2)
            if "location_coords" in batch and batch["location_coords"] is not None:
                location_coords = batch["location_coords"]

                # DataLoader会将元组转换为张量列表，需要重新组合
                if isinstance(location_coords, list) and len(location_coords) == 2:
                    # location_coords是[tensor([lon1, lon2, ...]), tensor([lat1, lat2, ...])]的形式
                    location_coords = torch.stack(location_coords, dim=1).float()  # (B, 2)
                elif not isinstance(location_coords, torch.Tensor):
                    location_coords = torch.tensor(location_coords, dtype=torch.float32)
                else:
                    location_coords = location_coords.float()
                location_coords = location_coords.to(device)
            else:
                location_coords = None
            

            # 在单时间维输入的B、C、H、W 中加一个时间维T=1 变为 B、C、T、H、W
            images = images.unsqueeze(2)

            # 前向传播(需要pixel_values 作为输入，并返回 loss（损失）)
            # 返回的mask的形状是 (B, N)，pred的形状是(B, N, P)
            loss, pred, mask = model(
                                    pixel_values=images.to(torch.float32),
                                    temporal_coords=temporal_coords, # temporal_coords # 可选参数
                                    location_coords=location_coords, # location_coords # 可选参数
                                    mask_ratio=mask_ratio
                                    )
            if torch.isnan(loss): # 加入对于loss为NaN的检查
                logging.warning(f"   NaN loss detected at batch {batch_idx}. Skipping update.")
                continue
            loss.backward() # 反向传播和优化
            # torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=5.0) # 加入梯度裁剪，限制梯度大小，防止梯度爆炸
            optimizer.step() # 更新模型参数
            
            # 更新进度条
            total_loss += loss.item()
            pbar.set_postfix({"loss": loss.item()})

            if batch_idx % 10 == 0: # 每10个batch记录一次
                logging.info(f"   Batch [{batch_idx+1}/{len(train_loader)}], Train Loss: {loss.item():.6f}")
    avg_train_loss = total_loss / len(dataloader) if len(dataloader) > 0 else 0# 计算在多个batch上的平均训练损失
    
    return avg_train_loss # 返回平均训练损失

# 验证函数
def validate(model, dataloader, device, mask_ratio=0.75):
    model.eval() # 模型进入评估模式
    total_loss = 0
    
    with torch.no_grad():
        with tqdm(dataloader, total=len(dataloader), desc="Validation") as pbar:
            for batch_idx, batch in enumerate(pbar):
                # 获取数据
                images = batch["image"].to(device)

                # 在单时间维输入的B、C、H、W 中加一个时间维T=1 变为 B、C、T、H、W
                images = images.unsqueeze(2)

                # 处理时间坐标：对于单时序输入，需要确保维度为 (B, T=1, 2)
                if "temporal_coords" in batch and batch["temporal_coords"] is not None:
                    temporal_coords = batch["temporal_coords"]
                    # DataLoader会将列表转换为张量列表，需要重新组合
                    if isinstance(temporal_coords, list) and len(temporal_coords) == 2:
                        # temporal_coords是[tensor([year1, year2, ...]), tensor([day1, day2, ...])]的形式
                        temporal_coords = torch.stack(temporal_coords, dim=1).float()  # (B, 2)
                    elif not isinstance(temporal_coords, torch.Tensor):
                        temporal_coords = torch.tensor(temporal_coords, dtype=torch.float32)
                    else:
                        temporal_coords = temporal_coords.float()
                    temporal_coords = temporal_coords.unsqueeze(1).to(device)  # (B, 2) -> (B, 1, 2)
                else:
                    temporal_coords = None

                # 处理位置坐标：维度为 (B, 2)
                if "location_coords" in batch and batch["location_coords"] is not None:
                    location_coords = batch["location_coords"]
                    # DataLoader会将元组转换为张量列表，需要重新组合
                    if isinstance(location_coords, list) and len(location_coords) == 2:
                        # location_coords是[tensor([lon1, lon2, ...]), tensor([lat1, lat2, ...])]的形式
                        location_coords = torch.stack(location_coords, dim=1).float()  # (B, 2)
                    elif not isinstance(location_coords, torch.Tensor):
                        location_coords = torch.tensor(location_coords, dtype=torch.float32)
                    else:
                        location_coords = location_coords.float()
                    location_coords = location_coords.to(device)
                else:
                    location_coords = None
                
                # 前向传播
                loss, pred, mask = model(
                                        pixel_values=images.to(torch.float32),
                                        temporal_coords=temporal_coords,
                                        location_coords=location_coords,
                                        mask_ratio=mask_ratio
                                        )
                
                total_loss += loss.item() # 损失叠加
                pbar.set_postfix({"loss": loss.item()}) # 更新进度条
                
    avg_val_loss = total_loss / len(dataloader) if len(dataloader) > 0 else 0 # 计算平均损失

    return avg_val_loss # 返回平均验证损失

# 定义一个自定义"数据模型"类
class ReconstructionDataModule(Dataset):
    # 初始化函数
    def __init__(self, dataset_path, 
                 transform=None, 
                 bands=[1,2,3,4,5,6], # 从 1 开始,对应 ["BLUE", "GREEN", "RED", "NIR_NARROW", "SWIR_1", "SWIR_2"]
                 file_extensions=('.tif', '.tiff', '.TIF', '.TIFF'),
                 mean=[], # 传入的数据集均值
                 std=[], # 传入的数据集标准差
                 ):
        self.image_dir = dataset_path
        self.transform = transform
        self.select_band = bands  # 设置使用波段（似乎应该输入列表，每个元素是index，而不是名称）
        self.image_files = []
        self.mean = mean
        self.std = std
        # 待检索文件夹内文件是否为空（若为空）
        if not os.path.isdir(dataset_path):
            logging.warning(f"Image directory not found: {dataset_path}. Dataset will be empty.")
            sys.exit(0) # 提前终止程序
        # 若不为空
        else:
            self.image_files = [os.path.join(dataset_path, f) for f in os.listdir(dataset_path)
                                if f.lower().endswith(file_extensions)]
        if not self.image_files:
            logging.warning(f"No images found in {dataset_path} with extensions {file_extensions}.")
            sys.exit(0) # 提前终止程序

    def __len__(self):
        return len(self.image_files) # 返回数据量

    def __getitem__(self, idx): # 用于dataloder获取
        img_path = self.image_files[idx] # 定位遍历结果中的idx对应文件的路径
        image,meta,coords = read_geotiff(img_path, self.select_band) # imagele类型为np.array，形状(C、H、W)
        temporal_coords = meta.get('time',[2021, 226]) 
        location_coords = coords # image的位置
        if self.transform:
            # 将numpy数组图像，由(C,H,W)调整为(H, W, C)格式
            image_np = np.transpose(image, (1, 2, 0))
            # 对数据进行z-score标准化
            image_np = (image_np - self.mean) / self.std # 进行z-score标准化
            # image_np = np.clip(image_np, -1, 1) # 限制像素值范围为 [-1, 1]
            # 使用 Albumentations 进行数据增强
            image = self.transform(image = image_np)["image"]

        # 返回数据字典
        return {
            "image": image,
            "temporal_coords": temporal_coords,
            "location_coords": location_coords,
            "id": idx
        }
    
    def plot(self, img_path):
        """
        影像可视化
        """
        rgb_indices = [3,2,1] # 规定RGB波段所在通道
        # 获取RGB图像
        rgb_image,_,_ = read_geotiff(file_path=img_path, bands=rgb_indices) # 此时形状为C、H、W
        # rgb_image = torch.tensor(rgb_image) 
        # 增强显示（2%线性拉伸显示）
        max_value = np.percentile(rgb_image, 99) # 上99%分位数作为最大值
        min_value = np.percentile(rgb_image, 1)  # 下1%分位数作为最小值
        # 先转换为 Tensor
        rgb_image = torch.from_numpy(rgb_image).float()
        rgb_image = torch.clamp((rgb_image - min_value) / (max_value - min_value),0,1)
        
        # 转至cpu并调整形状
        # rgb_image = rgb_image.to('cpu') # 切换至cpu
        rgb_image = rgb_image.permute(1, 2, 0) # 转换为 (H, W, C)
        rgb_image_np = rgb_image.numpy()
        
        # 绘制图像
        plt.figure(figsize=(8, 8))
        plt.imshow(rgb_image)
        plt.title(f"Sample: {img_path.split('/')[-1]}")
        plt.axis('off')
        plt.show()

# --- 0. 路径参数配置 ---
CONFIG_DIR = "./config.json"  # 传入的配置文件所在
OUT_CONFIG_DIR = "./config_Lora_output.json" # 导出加入Lora参数后的配置文件所在
PRETRAIN_MODEL_CHECKPOINT = "./Prithvi_EO_V2_300M_TL.pt" # 微调前预训练模型的权重文件
# Lora参数
LORA_DICT = {
    "use_lora": True, # 是否启用Lora
    "lora_rank": 16, # Lora微调所用低秩矩阵的维度
    "lora_alpha": 32, # 低秩矩阵的缩放因子
    "lora_dropout": 0.1, # LoRA 层的 dropout 概率
    'epochs': 50, # 微调用最大轮次
    "learning_rate": 1e-4, # 微调用初始学习率大小
    "batch_size": 64, # 微调用批大小 实际是越大越好
    "weight_decay": 0.05, # 优化器权重衰减率
    "num_workers": 0, # 并行数
    "target_modules": ["qkv", "proj", "fc1", "fc2", "decoder_pred"], # 微调目标层
            # "qkv"：匹配 Transformer 块中多头注意力机制的 Query/Key/Value 投影层。
            # "proj"：匹配 Transformer 块中多头注意力机制的输出投影层。
            # "fc1"：匹配 Transformer 块中 MLP (多层感知机) 的第一个线性层。
            # "fc2"：匹配 Transformer 块中 MLP 的第二个线性层。
            # "decoder_pred"：线性层，负责将解码器输出的特征映射到最终的像素值。
}
# 微调用数据存储路径
IMAGE_DIR_TRAIN = "./data/finetuning/building/train_images/" # 训练用数据目录
IMAGE_DIR_VAL = "./data/finetuning/building/val_images/"     # 验证用数据目录
IMAGE_DIR_TEST = "./data/finetuning/building/test_images/"   # 测试用数据目录
SAVE_MODEL_PATH = "./trained_models/building/" # 训练模型保存路径
USING_BANDS = ["BLUE", "GREEN", "RED", "NIR_NARROW", "SWIR_1", "SWIR_2"] # 要使用的波段


# --- 1. 模型加载与有关参数配置 ---
# PrithviMAE 的 __init__ 中已进行处理，此时的encoder冻结，decoder只有Lora层能被训练
config, model, device = load_model(config_path=CONFIG_DIR,
                                   out_config_path=OUT_CONFIG_DIR,
                                   Lora_config_dict=LORA_DICT,
                                   checkpoint_path=PRETRAIN_MODEL_CHECKPOINT
                                   )
logging.info("1.预训练模型加载及lora层注入成功")

# --- 2. 数据加载与预处理 ---
img_size = config.get('img_size', 224) # 获取数据尺寸
img_mean = config.get('mean') # 获取配置文件中记载的数据集的均值
img_std = config.get('std') # 获取配置文件中记载的数据集的标准差
# 定义图像变换处理策略
transforms = albumentations.Compose([ # 数据通用变换方式
    albumentations.Resize(img_size, img_size), # 将图像大小调整为 如224x224。
    albumentations.pytorch.transforms.ToTensorV2(), # 将图像转换为 PyTorch 张量
])
train_transforms = albumentations.Compose([ # 单独定义训练集数据的增强变换方式
    albumentations.HorizontalFlip(), # 随机水平翻转图像，增加数据多样性
    albumentations.VerticalFlip(), # 随机垂直翻转图像
    albumentations.RandomRotate90(), # 随机90度旋转
    albumentations.Resize(img_size, img_size), # 将图像大小调整为 如224x224
    albumentations.pytorch.transforms.ToTensorV2(), # 将图像转换为 PyTorch 张量
])
# 使用自定义数据模型创建数据加载器
train_dataset = ReconstructionDataModule(dataset_path=IMAGE_DIR_TRAIN, 
                                        transform=train_transforms,
                                        mean = img_mean,
                                        std = img_std,)
val_dataset = ReconstructionDataModule(dataset_path=IMAGE_DIR_VAL, 
                                        transform=transforms,
                                        mean = img_mean,
                                        std = img_std,)
test_dataset = ReconstructionDataModule(dataset_path=IMAGE_DIR_TEST, 
                                        transform=transforms,
                                        mean = img_mean,
                                        std = img_std,)

# 绘制几张train_dataset数据的RGB图
train_dataset.plot(train_dataset.image_files[1])

# 使用torch自带的dataloder创建数据加载器
train_loader = DataLoader(dataset = train_dataset, 
                          batch_size = config.get("batch_size"),
                          shuffle = True, 
                          num_workers = config.get("num_workers"), 
                          pin_memory=True,
                          drop_last = True,)
val_loader = DataLoader(dataset = val_dataset, 
                        batch_size=config.get("batch_size"), 
                        shuffle=False, 
                        num_workers = config.get("num_workers"), 
                        pin_memory=True,
                        drop_last = True,) # 是否丢弃不足一个batch的
test_loader = DataLoader(dataset = test_dataset, 
                         batch_size=1, # config.get("batch_size") 
                         shuffle=False, 
                         num_workers = config.get("num_workers"), 
                         pin_memory=True,
                         drop_last = False,)

# 记录数据情况
logging.info(f"Train dataset size: {len(train_dataset)}, Val dataset size: {len(val_dataset)}, Test dataset size: {len(test_dataset)}")
logging.info("2.数据加载与预处理定义成功")
if not len(train_dataset):
    logging.warning("Training dataset is empty. Program exit.")
    sys.exit(0) # 提前终止程序

# --- 3. 定义优化器和学习率调度器 ---
optimizer = torch.optim.AdamW( # 使用Adamw优化器
    filter(lambda p: p.requires_grad, model.parameters()),  # 过滤掉所有被冻结参数，只调整非冻结参数
        # filter(function, iterable)：根据function过滤一个可迭代对象iterable中的元素
        # lambda p: p.requires_grad：接收一个参数 p (代表一个模型参数)，并返回 p.requires_grad 的布尔值
        # p.requires_grad：PyTorch 张量的一个布尔属性，用于标记pytorch是否计算参数的梯度
        # model.parameters()：迭代器遍历模型中所有注册的参数（nn.Parameter 对象）
    lr=config.get("learning_rate"), 
    weight_decay=config.get("weight_decay")
    )
# 学习率调度器
lr_scheduler = torch.optim.lr_scheduler.CosineAnnealingLR( # 使用余弦退火学习率调度器
    optimizer, 
    T_max=config.get('epochs'),
    eta_min=1e-6
    )
# 将内容记录至日志
logging.info(f"Optimizer configured for {sum(p.numel() for p in model.parameters() if p.requires_grad):,} trainable parameters.")
logging.info("3.优化器和学习率调度器初始化成功")

# --- 4. 模型微调及在验证集上验证，保存loss最小模型 ---
os.makedirs(SAVE_MODEL_PATH, exist_ok=True) # 创建模型保存用文件夹
# 训练结果保存列表
best_val_loss = float('inf')
train_losses = []
val_losses = []
# 初始化最佳模型路径变量
best_model_path = None
best_lora_path = None
# mask_ratio_train = config.get('mask_ratio', 0.75) # Get mask_ratio from config, default 0.75
logging.info(f"Starting training for {config.get('epochs')} epochs...")# 开始日志记录
# 训练循环
for epoch in range(config.get('epochs')):
    print(f"\nEpoch {epoch+1}/{config.get('epochs')}")

    # 当前epoch上训练
    train_loss = train_epoch(model, train_loader, optimizer, 
                             device, mask_ratio = config.get('mask_ratio', 0.75))
    train_losses.append(train_loss)

    # 当前epoch上验证
    val_loss = validate(model, val_loader, 
                        device, mask_ratio = config.get('mask_ratio', 0.75))
    val_losses.append(val_loss)

    # 更新学习率（随时打印、记录日志）
    lr_scheduler.step()
    logging.info(f"Epoch [{epoch}/{config.get('epochs')}], Avg Train Loss: {train_loss:.6f}, Avg Val Loss: {val_loss:.6f}, LR: {optimizer.param_groups[0]['lr']:.6f}")
    # print(f"Epoch {epoch}: Avg Train Loss = {train_loss:.6f}, Avg Val Loss = {val_loss:.6f}, LR: {optimizer.param_groups[0]['lr']:.6f}")
    
    # 保存最佳模型（只保留当前最优，删除之前的）
    if val_loss < best_val_loss:
        best_val_loss = val_loss

        # 删除之前的最佳模型文件（如果存在）
        if best_model_path and os.path.exists(best_model_path):
            os.remove(best_model_path)
            logging.info(f"Removed previous best model: {best_model_path}")
        if best_lora_path and os.path.exists(best_lora_path):
            shutil.rmtree(best_lora_path)
            logging.info(f"Removed previous best LoRA model: {best_lora_path}")

        # 保存新的最佳完整模型
        model_save_name = f"best_model_epoch_{epoch}_valloss_{val_loss:.6f}.pt"
        best_model_path = os.path.join(SAVE_MODEL_PATH, model_save_name)
        torch.save(model.state_dict(), best_model_path)
        logging.info(f"Validation loss improved. Saved model to {best_model_path}")

        # 保存新的最佳LoRA模型
        lora_save_name = f"best_lora_epoch_{epoch}_valloss_{val_loss:.6f}"
        best_lora_path = os.path.join(SAVE_MODEL_PATH, lora_save_name)
        model.save_pretrained(best_lora_path, safe_serialization=True)
        logging.info(f"Validation loss improved. Saved LoRA model to {best_lora_path}")

# 绘制损失曲线
plt.figure(figsize=(10, 5))
plt.plot(train_losses, label='Training Loss')
plt.plot(val_losses, label='Validation Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.title('Training and Validation Loss')
plt.legend()
plt.savefig(os.path.join(SAVE_MODEL_PATH, "loss_curve.png"))
plt.show()

# --- 5. 模型加载及其在测试集上测试表现 ---
logging.info("Loading best model for testing...")

if best_lora_path and os.path.exists(best_lora_path):
    logging.info(f"Found best LoRA model: {best_lora_path}")

    # 使用LoRA模型路径加载
    base_model = PrithviMAE(**config).to(device)
    best_model = PeftModel.from_pretrained(base_model, best_lora_path)
    best_model.eval()
    logging.info("Successfully loaded LoRA model from pretrained path")

else:
    logging.warning("No best model found for testing!")
    best_model = model  # 使用当前模型进行测试
# 在测试集上评估
test_loss = validate(best_model, test_loader, device)
logging.info(f"--- Test Results ---")
logging.info(f"\nAverage Test Loss: {test_loss:.6f}")
# print(f"Test Loss: {test_loss:.6f}")
# print(f"Fine-tuning script finished.")
logging.info("Fine-tuning script finished.")