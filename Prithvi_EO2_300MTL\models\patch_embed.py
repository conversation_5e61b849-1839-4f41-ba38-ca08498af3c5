
"""
Patch嵌入模块
Patch embedding module for Prithvi model
"""

import logging
from typing import Tuple
import torch
import torch.nn as nn

# 将3D输入（如时空数据）分割成patches并进行嵌入
class PatchEmbed(nn.Module):
    """3D version of timm.models.vision_transformer.PatchEmbed
    timm.models.vision_transformer.PatchEmbed的3D版本
    用于将3D输入（如时空数据）分割成patches并进行嵌入
    """
    def __init__(
            self,
            input_size: Tuple[int, int, int] = (1, 224, 224), # 默认输入尺寸
            patch_size: Tuple[int, int, int] = (1, 16, 16), # 默认 Patch 尺寸 (T,H,W) 
            in_chans: int = 3, # 输入通道数，例如 RGB 图像为 3
            embed_dim: int = 768, # 每个 Patch 嵌入后的维度
            norm_layer: nn.Module | None = None, # 可选的归一化层
            flatten: bool = True, # 是否将 Patch 嵌入展平为序列
            bias: bool = True, # 卷积层是否使用偏置
    ):
        super().__init__() # 调用 nn.Module 的构造函数初始化
        self.input_size = input_size
        self.patch_size = patch_size # 形状为 (T, H, W)
        self.grid_size = [s // p for s, p in zip(self.input_size, self.patch_size)] # 计算在每个维度上（时间帧、高度、宽度）有多少个 Patch
        self.num_patches = self.grid_size[0] * self.grid_size[1] * self.grid_size[2] # 计算总的 Patch 数量（不包括 CLS token）
        self.flatten = flatten # 展平与否
        # 定义 Patch 嵌入的核心操作：一个 3D 卷积层(将 3D 输入数据分割成 Patch，并对每个 Patch 进行线性投影)
        # 相当于对输入进行patch分割，并对每个分割后的patch进行线性嵌入输出一个向量（长度为 embed_dim，相当于有这么多个卷积核所以是这么多个维度）
            # 1. `in_chans`: 输入数据的通道数。
            # 2. `embed_dim`: 卷积层的输出通道数，也是每个 Patch 嵌入后的特征维度。
            # 3. `kernel_size=patch_size`: 卷积核的大小与 Patch 的大小相同。
            # 4. `stride=patch_size`: 卷积的步长也与 Patch 的大小相同(不重叠的 Patch 分割)
            # 5. `bias`: 是否使用偏置。
        self.proj = nn.Conv3d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size, bias=bias)
        # 如果提供了 `norm_layer`（例如 `nn.LayerNorm`），则初始化该层，否则为空
        self.norm = norm_layer(embed_dim) if norm_layer else nn.Identity() 

    # 前向传播函数
    def forward(self, x):
        B, C, T, H, W = x.shape # 获取输入张量的形状信息
        # 检查输入尺寸是否能够被 Patch 大小整除
        if T / self.patch_size[0] % 1 or H / self.patch_size[1] % 1 or W / self.patch_size[2] % 1:
            logging.warning(f"Input {x.shape[-3:]} is not divisible by patch size {self.patch_size}."
                            f"The border will be ignored, add backbone_padding for pixel-wise tasks.")
        x = self.proj(x) # 执行卷积操作，将输入数据分割成 Patch，并进行线性投影
        if self.flatten: # 如果 `flatten` 为 True（默认行为），则将 Patch 嵌入展平
            x = x.flatten(2).transpose(1, 2)  # B,C,T,H,W -> B,C,L -> B,L,C
            # `flatten(2)`：从第 2 个维度开始展平 B,C,T,H,W -> B,C,(T*H*W) -> B,C,L
            # `transpose(1, 2)`：交换第 1 维和第 2 维。 
        x = self.norm(x)
        return x