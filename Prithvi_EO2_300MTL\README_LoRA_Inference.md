# LoRA模型推理脚本使用说明

## 概述

`inference_lora_load.py` 是一个专门用于加载和推理LoRA微调后的Prithvi-EO-2.0模型的脚本。该脚本采用外挂LoRA权重的方式，方便后续的多类别微调后权重挂载，支持单时序图像的MAE重建任务。

## 主要特性

- **外挂LoRA权重**: 支持动态加载不同的LoRA适配器，无需重新训练基础模型
- **单时序优化**: 针对单时序输入进行了优化，适合单图像MAE任务
- **灵活的输出格式**: 支持RGB和多波段输出
- **完整的推理流程**: 包含数据加载、模型推理、结果保存的完整流程
- **中文注释**: 所有代码都有详细的中文注释，便于理解和维护

## 文件结构

```
├── Prithvi_EO2_300MTL/         # 主要模型文件夹
│   ├── inference_lora_load.py      # 主推理脚本
│   ├── README_LoRA_Inference.md    # 本说明文档
│   ├── trained_models/             # LoRA模型存储目录
│   │   └── EuroSAT/                # 示例LoRA适配器文件夹
│   │       └── best_lora_epoch_7_valloss_0.087172/
│   │           ├── adapter_config.json
│   │           ├── adapter_model.safetensors
│   │           └── README.md
│   ├── config.json                 # 模型配置文件
│   └── Prithvi_EO_V2_300M_TL.pt   # 基础预训练模型权重
└── example_lora_inference.py       # 使用示例脚本（项目根目录）
```

## 安装依赖

确保您已安装以下Python包：

```bash
pip install torch torchvision
pip install peft transformers
pip install rasterio
pip install einops
pip install numpy pandas
pip install pyyaml
pip install tqdm
```

## 使用方法

### 方法1: 命令行使用

在 `Prithvi_EO2_300MTL` 目录下运行：

```bash
cd Prithvi_EO2_300MTL
python inference_lora_load.py \
    --data_files ./examples/Mexico_HLS.S30.T13REM.2018026T173609.v2.0_cropped.tif \
    --config_path ./config.json \
    --base_checkpoint ./Prithvi_EO_V2_300M_TL.pt \
    --lora_adapter_path ./trained_models/EuroSAT/best_lora_epoch_7_valloss_0.087172 \
    --output_dir ./output_lora \
    --rgb_outputs \
    --mask_ratio 0.75 \
    --seed 42
```

### 方法2: Python脚本调用

#### 简单推理
```python
from Prithvi_EO2_300MTL.inference_lora_load import inference_with_lora_adapter

# 使用便捷函数进行推理
inference_with_lora_adapter(
    lora_adapter_path="./Prithvi_EO2_300MTL/trained_models/EuroSAT/best_lora_epoch_7_valloss_0.087172",
    data_files=["./Prithvi_EO2_300MTL/examples/Mexico_HLS.S30.T13REM.2018026T173609.v2.0_cropped.tif"],
    output_dir="./Prithvi_EO2_300MTL/output_lora",
    rgb_outputs=True,
    mask_ratio=0.75,
    seed=42
)
```

#### 高级推理
```python
from Prithvi_EO2_300MTL.inference_lora_load import main

# 使用主函数进行更精细的控制
main(
    data_files=["./Prithvi_EO2_300MTL/examples/Mexico_HLS.S30.T13REM.2018026T173609.v2.0_cropped.tif"],
    config_path="./Prithvi_EO2_300MTL/config.json",
    base_checkpoint="./Prithvi_EO2_300MTL/Prithvi_EO_V2_300M_TL.pt",
    lora_adapter_path="./Prithvi_EO2_300MTL/trained_models/EuroSAT/best_lora_epoch_7_valloss_0.087172",
    output_dir="./Prithvi_EO2_300MTL/output_lora",
    rgb_outputs=True,
    mask_ratio=0.75,
    input_indices=None,  # 使用所有波段
    seed=42
)
```

### 方法3: 运行示例脚本

在项目根目录下运行：

```bash
python example_lora_inference.py
```

该示例脚本位于项目根目录，包含了三个不同的使用示例：
- 示例1：简单LoRA推理
- 示例2：高级LoRA推理（更多控制选项）
- 示例3：批量推理多个LoRA模型

## 参数说明

### 必需参数
- `--lora_adapter_path`: LoRA适配器权重文件夹路径（必需）
- `--data_files`: 输入数据文件路径列表

### 可选参数
- `--config_path`: 配置文件路径（默认: config.json）
- `--base_checkpoint`: 基础预训练模型权重路径（默认: Prithvi_EO_V2_300M_TL.pt）
- `--output_dir`: 输出目录（默认: output_lora）
- `--rgb_outputs`: 是否只输出RGB通道（标志参数）
- `--mask_ratio`: 掩码比例（默认: 0.75）
- `--input_indices`: 输入波段索引列表（默认: 使用所有波段）
- `--seed`: 随机种子（默认: None）

## 输出文件

根据`rgb_outputs`参数的设置，脚本会生成不同的输出文件：

### RGB输出模式 (`--rgb_outputs`)
- `original_rgb_t0.tiff`: 原始RGB图像
- `predicted_rgb_t0.tiff`: 重建的RGB图像
- `masked_rgb_t0.tiff`: 掩码应用后的RGB图像

### 多波段输出模式
- `predicted_t0.tiff`: 重建的多波段图像
- `masked_t0.tiff`: 掩码图像

## 核心功能说明

### 1. LoRA模型加载
- 自动加载基础预训练模型
- 动态挂载LoRA适配器
- 支持不同尺寸的位置编码插值

### 2. 数据处理
- 支持多种遥感图像格式
- 自动进行数据标准化
- 智能填充处理不规则尺寸

### 3. 推理流程
- 滑动窗口处理大图像
- 批量推理优化
- 结果拼接和后处理

### 4. 结果保存
- 保持原始地理信息
- 支持多种输出格式
- 自动创建输出目录

## 注意事项

1. **内存使用**: 大图像可能需要较多内存，建议根据GPU内存调整批次大小
2. **文件路径**: 确保所有输入文件路径正确且文件存在
3. **LoRA兼容性**: 确保LoRA适配器与基础模型兼容
4. **单时序优化**: 该脚本针对单时序输入优化，多时序输入可能效果不佳
5. **导入方式**:
   - 在项目根目录运行脚本时，使用绝对导入：`from Prithvi_EO2_300MTL.inference_lora_load import ...`
   - 在 `Prithvi_EO2_300MTL` 目录内运行时，使用相对导入：`from inference_lora_load import ...`

## 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减少批次大小
   - 使用CPU推理（自动检测）

2. **文件路径错误**
   - 检查所有文件路径是否正确
   - 确保LoRA适配器文件夹包含必要文件

3. **模型加载失败**
   - 检查配置文件格式
   - 确认基础模型权重完整

4. **推理结果异常**
   - 检查输入数据格式
   - 验证数据预处理参数

## 扩展使用

### 批量处理多个模型
```python
lora_models = [
    "./best_models/model1",
    "./best_models/model2",
    "./best_models/model3"
]

for model_path in lora_models:
    inference_with_lora_adapter(
        lora_adapter_path=model_path,
        data_files=["./input.tif"],
        output_dir=f"./output_{os.path.basename(model_path)}"
    )
```

### 自定义输出路径
```python
output_dict = {
    "OriRGB_dir": "./custom_original.tiff",
    "recon_img_dir": "./custom_reconstructed.tiff",
    "mask_img_dir": "./custom_masked.tiff"
}

main(..., output_dict=output_dict)
```

## 联系和支持

如有问题或建议，请查看代码注释或联系开发者。
