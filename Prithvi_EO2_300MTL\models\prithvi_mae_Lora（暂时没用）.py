"""
Prithvi MAE主模型
Prithvi MAE main model implementation
"""
from functools import partial
from typing import List, Tuple
import torch
import torch.nn as nn
from einops import rearrange
from timm.layers import to_2tuple

# 导入 PEFT 库
from peft import get_peft_model, LoraConfig, TaskType

# 读取已有脚本
from .encoder import PrithviViT
from .decoder import MAEDecoder

# 封装编码器解码器后的模型
class PrithviMAE(nn.Module):
    """ Prithvi Masked Autoencoder
    Prithvi掩码自编码器，用于地球观测数据的自监督学习
    它封装了 PrithviViT（编码器）和 MAEDecoder（解码器），
    负责管理整个 MAE 的前向传播、掩码操作、重建以及损失计算。
    """
    def __init__(self,
                 img_size: int | Tuple[int, int] = 224, # 输入图像的空间尺寸 (H, W)
                 patch_size: int | Tuple[int, int, int] = (1, 16, 16), # Patch 的尺寸 (T_p, H_p, W_p)
                 num_frames: int = 3, # 输入序列的时间帧数
                 in_chans: int = 3, # 输入图像的通道数
                 embed_dim: int = 1024, # 编码器的嵌入维度
                 depth: int = 24, # 编码器 Transformer 块的数量
                 num_heads: int = 16, # 编码器 Transformer 块中的注意力头数
                 decoder_embed_dim: int = 512, # 解码器的嵌入维度
                 decoder_depth: int = 8, # 解码器 Transformer 块的数量
                 decoder_num_heads: int = 16, # 解码器 Transformer 块中的注意力头数
                 mlp_ratio: float = 4., # MLP 层的隐藏层维度与嵌入维度之比
                 norm_layer: nn.Module = partial(torch.nn.LayerNorm, eps=1e-6), # 归一化层类型
                 norm_pix_loss: bool = False, # 是否对像素损失进行归一化
                 coords_encoding: List[str] | None = None, # 坐标编码类型列表 ['time', 'location']
                 coords_scale_learn: bool = False, # 坐标编码的缩放因子是否可学习
                 encoder_only: bool = False,  # 是否仅使用编码器（不包括解码器）
                 
                 # === 新增 LoRA 相关参数 ===
                 use_lora: bool = False, # 是否启用Lora
                 lora_rank: int = 8, # Lora微调所用低秩矩阵的维度
                 lora_alpha: int = 16, # 低秩矩阵的缩放因子
                 lora_dropout: float = 0.0, # LoRA 层的 dropout 概率
                 
                 **kwargs, # 其他参数
                 ):
        super().__init__()
        
        # 初始化编码器 (PrithviViT)
        self.encoder = PrithviViT(
            img_size=img_size,
            patch_size=patch_size,
            num_frames=num_frames,
            in_chans=in_chans,
            embed_dim=embed_dim,
            depth=depth,
            num_heads=num_heads,
            mlp_ratio=mlp_ratio,
            norm_layer=norm_layer,
            coords_encoding=coords_encoding,
            coords_scale_learn=coords_scale_learn,
        )

        self.encoder_only = encoder_only

        # 初始化解码器 (MAEDecoder)
        if not encoder_only:
            self.decoder = MAEDecoder(
                patch_size=patch_size,
                grid_size=self.encoder.patch_embed.grid_size, # 从编码器的 PatchEmbed 获取原始 Patch 网格尺寸
                in_chans=in_chans,
                encoder_embed_dim=embed_dim, # 解码器输入维度与编码器输出维度匹配
                decoder_embed_dim=decoder_embed_dim,
                depth=decoder_depth,
                num_heads=decoder_num_heads,
                mlp_ratio=mlp_ratio,
                norm_layer=norm_layer,
                coords_encoding=coords_encoding,
                coords_scale_learn=coords_scale_learn,
            )
        else:
            self.decoder = nn.Identity() # 如果只用编码器，解码器不执行任何操作
        
        # 是否使用归一化像素损失 (MAE 论文中的一个重要技巧，有助于提高重建质量)    
        self.norm_pix_loss = norm_pix_loss
        
        # LoRA 层注入
        # ==========================
        self.use_lora = use_lora # 是否启用Lora参数初始化
        if self.use_lora:
            # # 冻结编码器参数 peft.get_peft_model 默认会冻结基座模型的参数，所以我们这里可以不手动冻结
            # # 但为了明确意图，可以加一个循环来冻结编码器
            for param in self.encoder.parameters():
                param.requires_grad = False
            print("Encoder parameters frozen.")

            # 定义 LoRA 配置，目标是解码器中的线性层 由于来源于默认类，故包含 'qkv', 'proj', 'fc1', 'fc2'
            lora_config_decoder = LoraConfig(
                r=lora_rank, # 低秩的维度
                lora_alpha=lora_alpha, # 低秩矩阵的缩放因子
                target_modules=["qkv", "proj", "fc1", "fc2", "pred_head"], # 添加至的位置
                    # "qkv"：匹配 Transformer 块中多头注意力机制的 Query/Key/Value 投影层。
                    # "proj"：匹配 Transformer 块中多头注意力机制的输出投影层。
                    # "fc1"：匹配 Transformer 块中 MLP (多层感知机) 的第一个线性层。
                    # "fc2"：匹配 Transformer 块中 MLP 的第二个线性层。
                    # "pred_head"：线性层，负责将解码器输出的特征映射到最终的像素值。
                lora_dropout=lora_dropout, # 应用于 LoRA 适配器中矩阵 A 的 Dropout 概率
                bias="none", # 是否对偏置 (bias) 项微调
                task_type=TaskType.FEATURE_EXTRACTION, # 对于特征变换层通常选这个
            )
            # 对解码器应用 LoRA
            self.decoder = get_peft_model(self.decoder, lora_config_decoder)
            print(f"LoRA applied to MAEDecoder with rank={lora_rank}, alpha={lora_alpha}.")
            self.decoder.print_trainable_parameters() # 打印可训练参数量
        # ==========================


    def patchify(self, pixel_values):
        """
        将原始的 5D (B, C, T, H, W) 像素值数据分割并展平为 3D (B, num_patches, patch_pixel_dim) 的 Patch 序列。
        这是 MAE 进行自监督学习前对输入数据的预处理步骤。

        Args:
            pixel_values (torch.FloatTensor of shape `(batch_size, num_channels, time, height, width)`):
                Pixel values.
                像素值，形状为(batch_size, num_channels, time, height, width)。

        Returns:
            torch.FloatTensor of shape `(batch_size, num_patches, patch_size[0]*patch_size[1]*patch_size[2] * num_channels)`:
                Patchified pixel values.
                分块后的像素值，形状为(batch_size, num_patches, patch_size[0]*patch_size[1]*patch_size[2] * num_channels)。
        """
        # 将输入图像分割成不重叠的图像块，用于MAE的重建任务
        patch_size_t, patch_size_h, patch_size_w = self.encoder.patch_embed.patch_size
        num_channels = self.encoder.in_chans

        # 使用 `einops.rearrange` 进行高效且可读的张量重排和分块
        patchified_pixel_values = rearrange(pixel_values, 'b c (t s) (h p) (w q) -> b (t h w) (s p q c)',
                                            c=num_channels, s=patch_size_t, p=patch_size_h, q=patch_size_w)


        return patchified_pixel_values

    def unpatchify(self, patchified_pixel_values, image_size: Tuple[int, int] | None = None):
        """
        将展平后的 Patch 序列（重建结果）重新组合成原始的 5D 像素值张量。
        这是 `patchify` 的逆操作，用于将解码器的输出转换为可视图形的格式。

        Args:
            patchified_pixel_values (`torch.FloatTensor` of shape
                `(batch_size, num_patches, patch_size[0]*patch_size[1]*patch_size[2] * num_channels)`:
                Patchified pixel values.
                分块后的像素值，形状为(batch_size, num_patches, patch_size[0]*patch_size[1]*patch_size[2] * num_channels)。
            image_size (`Tuple[int, int]`, *optional*):
                Original image size.
                原始图像尺寸，形状为(H, W)。

        Returns:
            `torch.FloatTensor` of shape `(batch_size, num_channels, height, width)`:
                Pixel values.
                重建后的像素值，形状为(batch_size, num_channels, height, width)。
        """
        # 将分块的特征重新组合成完整的图像，是patchify的逆操作
        patch_size_t, patch_size_h, patch_size_w = self.encoder.patch_embed.patch_size
        # 确保 image_size 是 (H, W) 的元组形式
        image_size = to_2tuple(image_size) if image_size is not None else self.encoder.img_size
        original_height, original_width = image_size
        # 计算在高度和宽度维度上有多少个 Patch
        num_patches_h = original_height // patch_size_h
        num_patches_w = original_width // patch_size_w
        num_channels = self.encoder.in_chans
        
        # 逆转 `rearrange` 操作，将展平的 Patch 重新组合成 5D 图像
        pixel_values = rearrange(patchified_pixel_values, 'b (t h w) (s p q c) -> b c (t s) (h p) (w q)',
                                 c=num_channels, h=num_patches_h, w=num_patches_w,
                                 s=patch_size_t, p=patch_size_h, q=patch_size_w)
        return pixel_values

    def forward_loss(self, pixel_values, pred, mask):
        """
        计算MAE的重建损失
        
        Args:
            pixel_values (`torch.FloatTensor` of shape `(batch_size, num_channels, time, height, width)`):
                Pixel values.
                原始像素值。
            pred (`torch.FloatTensor` of shape `(batch_size, num_patches, patch_size[0]*patch_size[1]*patch_size[2] * num_channels)`:                Predicted pixel values.
                预测的像素值，即模型重建的图像块。
            mask (`torch.FloatTensor` of shape `(batch_size, sequence_length)`):                Tensor indicating which patches are masked (1) and which are not (0).
                用于预测的掩码，指示哪些图像块被掩盖。

        Returns:
            `torch.FloatTensor`: Pixel reconstruction loss.
            损失值，衡量重建质量的指标。
        """
        # 将输入图像或视频划分为多个小块（patches），每个小块（patch）展平成一个 Token，形状变成 (B, num_patches, 每个patch的token_dim)
        # 类似于（batch_size,空间立方体切出多少个patch，每个patch又是由多少个像素点组成）
        # 传入的pixel_values形状可能为B,C,T,H,W
        target = self.patchify(pixel_values) # 输出形状是(B, num_patches, 每个patch的token_dim)

        # 对每个 patch 进行归一化
        if self.norm_pix_loss:
            mean = target.mean(dim=-1, keepdim=True) # 对最后一个维度内求均值 计算后形状为: (B, num_patches, 1)
            var = target.var(dim=-1, keepdim=True) # 对最后一个维度内求标准差 计算后形状为: (B, num_patches, 1)
            target = (target - mean) / (var + 1.0e-6) ** 0.5 # 输出形状是(B, num_patches, 每个patch的token_dim)

        # 计算 MSE 损失
        loss = (pred - target) ** 2 # 此时形状为(B, num_patches, 每个patch的token_dim)
        loss = loss.mean(dim=-1)  # [N, L], mean loss per patch（对每个 patch 内所有像素的损失的均值）
        loss = (loss * mask).sum() / mask.sum()  # mean loss on removed patches
        return loss # loss为标量，反映的是被遮挡 patch 的平均重建误差

    def forward(
        self,
        pixel_values: torch.Tensor,
        temporal_coords: None | torch.Tensor = None,
        location_coords: None | torch.Tensor = None,
        mask_ratio: float = 0.75
    ):
        """
        掩码自编码器的前向传播方法，实现了整个MAE的工作流程
        
        Args:
            pixel_values (torch.FloatTensor): 
                原始输入像素值，形状为(batch_size, num_channels, time, height, width)如果时间维度为 1 且输入是 4D，会自动扩展为 5D。
            temporal_coords(torch.FloatTensor, optional): 
                时间坐标（年份，一年中的第几天），形状为(batch_size, time, 2)。
            location_coords  (torch.FloatTensor, optional): 
                位置坐标（纬度，经度），形状为(batch_size, 2)。
            mask_ratio(float, optional):
                要被掩码的图像块比例，默认为0.75（即75%的图像块被掩码）。

        Returns:
            tuple: 包含损失值、预测的像素值和掩码的元组。
                   - `loss`: 计算得到的重建损失。
                   - `pred`: 解码器重建的 Patch 序列像素值。
                   - `mask`: 用于掩码操作的二进制掩码张量。
        """
        # MAE的前向传播过程：编码被掩码的图像，然后解码并重建完整图像

        # 兼容 4D 输入（如单帧图像），自动增加时间维度
        if len(pixel_values.shape) == 4 and self.encoder.patch_embed.input_size[0] == 1:
            # add time dim
            pixel_values = pixel_values.unsqueeze(2)
        # 1. 编码器前向传播：对输入进行掩码并编码可见 Patch
        latent, mask, ids_restore = self.encoder(pixel_values, temporal_coords, location_coords, mask_ratio)
        # 2. 解码器前向传播：从编码器输出和 Mask token 重建所有 Patch
            # `pred`: 解码器预测的 Patch 序列像素值
        pred = self.decoder(latent, ids_restore, temporal_coords, location_coords, input_size=pixel_values.shape)
        # 3. 计算损失：根据预测值和原始目标计算重建损失
        loss = self.forward_loss(pixel_values, pred, mask)
        return loss, pred, mask

    def forward_features(
        self,
        x: torch.Tensor,
        temporal_coords: None | torch.Tensor = None,
        location_coords: None | torch.Tensor = None,
    ) -> List[torch.Tensor]:
        """
        用于特征提取的前向传播方法。
        当模型经过 MAE 预训练后，可以单独使用编码器作为特征提取器。
        这个方法直接调用编码器的 `forward_features` 方法，不涉及掩码和解码。

        Args:
            x (torch.Tensor): 输入数据，形状 `(batch_size, num_channels, time, height, width)`。
            temporal_coords (Optional[torch.Tensor]): 时间坐标。
            location_coords (Optional[torch.Tensor]): 位置坐标。

        Returns:
            List[torch.Tensor]: 编码器输出的特征，通常是 CLS token 和 Patch 特征。
        """
        return self.encoder.forward_features(x, temporal_coords, location_coords)
