import os
# from osgeo import gdal # gdal is not explicitly used in the provided code, keeping rasterio is sufficient
import rasterio
from tqdm import tqdm
import numpy as np
import torch
import torch.nn.functional as F
import math
from skimage.metrics import structural_similarity # Added for SSIM calculation

# 获取当前文件所在地址
current_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(current_dir)  # 把当前工作目录切换到脚本所在目录
print("当前文件所在目录是：", current_dir)

# 参数设置
#   原始影像存放文件夹
DATA_FOLDER_NAME = '0.Data' # 存储用数据文件夹
#   重建图像存储文件夹名称
RECONSTRACT_OUTPUT_FOLDER_NAME = "Reconstract_Output"
#   多图平均像素误差图存储文件夹 (新的文件夹名)
AVERAGE_PIXEL_LOSS_TIFF_FOLDER = "Average_PixelLoss_tiff"
#   多图平均 disim 图存储文件夹 (新的文件夹)
AVERAGE_DISIM_TIFF_FOLDER = "Average_Disim_tiff"
#   多图平均 综合误差图存储文件夹 (新的文件夹，使用原Average_Loss_tiff的命名规则)
AVERAGE_COMBINED_LOSS_TIFF_FOLDER = "Average_Loss_tiff"

#   自定义方法字典 (用于滤波、SSIM等)
METHOD_DICT = {
    "pixel_loss_type": "SE", # 像素误差计算类型: "SE" (Square Error) 或 "AE" (Absolute Error)
    "filter_method" : "Gaussian_kernel", # 核类型
    "sigma" : 0.7, # σ值
    "kernel_size" : 7, # 核大小（得是奇数）
    "ssim_win_size": 11, # SSIM 计算窗口大小 (通常是奇数)
    "ssim_data_range": 1, # SSIM 计算的数据范围 (这里简化处理为先0-1归一化再处理)
}

# 路径构建
#   构建原始图像存放文件夹路径
Origin_DataFolder_dir = os.path.join(current_dir, DATA_FOLDER_NAME)
#   构建重建图像导出文件夹路径
Reconstract_DataFolder_dir = os.path.join(current_dir, RECONSTRACT_OUTPUT_FOLDER_NAME)
#   构建平均像素误差图存储文件夹路径
AVPixelLoss_DataFolder_dir = os.path.join(current_dir, AVERAGE_PIXEL_LOSS_TIFF_FOLDER)
#   构建平均 disim 图存储文件夹路径
AVDisim_DataFolder_dir = os.path.join(current_dir, AVERAGE_DISIM_TIFF_FOLDER)
#   构建平均 综合误差图存储文件夹路径
AVCombinedLoss_DataFolder_dir = os.path.join(current_dir, AVERAGE_COMBINED_LOSS_TIFF_FOLDER)

# 文件夹检查与创建
if not os.path.exists(AVPixelLoss_DataFolder_dir):
    os.makedirs(AVPixelLoss_DataFolder_dir)
if not os.path.exists(AVDisim_DataFolder_dir):
    os.makedirs(AVDisim_DataFolder_dir)
if not os.path.exists(AVCombinedLoss_DataFolder_dir):
    os.makedirs(AVCombinedLoss_DataFolder_dir)

# 检查 CUDA 是否可用
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用的设备是: {device}")
if device.type == 'cuda':
    print(f"GPU 名称: {torch.cuda.get_device_name(0)}")
    print(f"GPU 显存总量: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.2f} GB")


# 辅助函数：创建高斯核
def create_gaussian_kernel(kernel_size, sigma):
    """创建 2D 高斯核."""
    # 生成核覆盖范围(-3,3)，在这个范围内生成 kernel_size 个点
    ax = np.linspace(-(kernel_size - 1) / 2., (kernel_size - 1) / 2., kernel_size)
    # 实现了高斯函数的一维形式
    gauss = np.exp(-0.5 * np.square(ax) / np.square(sigma))
    # 计算两个向量的外积生成一个二维矩阵 kernel （即滤波核）
    kernel = np.outer(gauss, gauss)
    # 滤波器归一化
    kernel = kernel / np.sum(kernel)
    # 将 NumPy 数组转换为 PyTorch tensor, 并增加批次和通道维度（）
    # .unsqueeze(0)：表示在 Tensor 的最前面增加一个维度，这里即增加了两个维度
    # 这里将高斯核也两个维度，为了后面维度 conv2d 兼容（[C_out, C_in / groups, kH, kW]）
    return torch.from_numpy(kernel).float().unsqueeze(0).unsqueeze(0) # 形状 [1, 1, kernel_size, kernel_size]

# 定义一个【多波段像素误差(SE 或 MAE)+滤波】 函数
def tifflist_pixel_error(
    reconstruct_img_paths: list[str], # 重建图像路径列表 (针对同一原始图像)
    origin_img_path: str, # 原始图像路径
    method_dict : dict, # 方法字典 (包含 pixel_loss_type 和滤波参数)
):
    """
    计算并滤波每个重建图像与原始图像之间的逐波段像素误差图 (SE 或 MAE)，
    然后将滤波后的逐波段误差图求和。

    Args:
        reconstruct_img_paths (list[str]): 重建图像的文件路径列表。
        origin_img_path (str): 原始图像的文件路径。
        method_dict (dict): 指定像素误差类型、滤波方法和参数的字典。

    Returns:
        list[np.ndarray]: 一个 NumPy 数组列表，每个数组代表一个重建图像的
                         滤波后的波段求和像素误差图。
    """
    print(f"\n正在处理原始图像: {origin_img_path}")
    print(f"找到 {len(reconstruct_img_paths)} 个重建图像。")

    pixel_error_tiff_list = []
    pixel_loss_type = method_dict.get("pixel_loss_type", "SE").upper() # 默认 SE
    sigma = method_dict.get("sigma", 1.4)
    kernel_size = method_dict.get("kernel_size", 2*math.ceil(3*sigma)+1)
    gaussian_kernel = create_gaussian_kernel(kernel_size, sigma).to(device)
    padding_amount = kernel_size // 2
    pad_tuple = (padding_amount, padding_amount, padding_amount, padding_amount)

    print(f"使用 {pixel_loss_type} 计算像素误差，高斯核大小: {kernel_size}, sigma: {sigma}")

    try:
        with rasterio.open(origin_img_path) as src_orig:
            origin_data = src_orig.read().astype(np.float32)
            origin_tensor = torch.from_numpy(origin_data).to(device) # 形状 [C, H, W]

            num_bands = origin_tensor.shape[0]
            print(f"原始图像有 {num_bands} 个波段。")

            for recon_path in tqdm(reconstruct_img_paths, desc=f"计算{pixel_loss_type} 和滤波"):
                try:
                    with rasterio.open(recon_path) as src_recon:
                        recon_data = src_recon.read().astype(np.float32)
                        recon_tensor = torch.from_numpy(recon_data).to(device) # 形状 [C, H, W]

                        if origin_tensor.shape != recon_tensor.shape:
                            print(f"警告: 原始图像 ({origin_tensor.shape}) 与重建图像 ({recon_tensor.shape}) {recon_path} 尺寸或波段数不匹配。跳过。")
                            continue

                        # 计算逐波段像素误差
                        diff = origin_tensor.unsqueeze(0) - recon_tensor.unsqueeze(0) # 形状 [1, C, H, W]
                        if pixel_loss_type == "SE":
                            error_per_band = torch.square(diff) # (I^c - R_i^c)^2
                        elif pixel_loss_type == "AE":
                            error_per_band = torch.abs(diff) # |I^c - R_i^c|
                        else:
                             print(f"未知像素误差类型: {pixel_loss_type}。跳过 {recon_path}。")
                             continue

                        C = error_per_band.shape[1]

                        # 1. 对逐波段误差进行反射填充
                        padded_error_per_band = F.pad(error_per_band, pad_tuple, mode='reflect') # 形状 [1, C, 填充后H, 填充后W]
                        # 2. 准备高斯滤波核用于逐通道卷积
                        channel_wise_gaussian_kernel = gaussian_kernel.repeat(C, 1, 1, 1)
                        # 3. 应用逐通道高斯滤波
                        filtered_error_per_band = F.conv2d(padded_error_per_band, channel_wise_gaussian_kernel, padding=0, groups=C) # 输出形状 [1, C, H, W]
                        # 4. 将滤波后的逐波段误差沿着波段维度求和
                        total_filtered_error = torch.sum(filtered_error_per_band, dim=1, keepdim=True) # 形状 [1, 1, H, W]

                        # 转换回 NumPy 并移除批次和通道维度
                        pixel_error_tiff_list.append(total_filtered_error.squeeze().cpu().numpy()) # 形状 [H, W]

                except rasterio.errors.RasterioIOError as e:
                    print(f"读取重建图像 {recon_path} 时出错: {e}")
                except Exception as e:
                    print(f"处理 {recon_path} 时发生错误: {e}")

    except rasterio.errors.RasterioIOError as e:
        print(f"读取原始图像 {origin_img_path} 时出错: {e}")
    except Exception as e:
        print(f"处理原始图像 {origin_img_path} 时发生错误: {e}")

    return pixel_error_tiff_list

# 定义一个【多波段 SSIM + disim_map】 函数
def tifflist_disim_map(
    reconstruct_img_paths: list[str], # 重建图像路径列表 (针对同一原始图像)
    origin_img_path: str, # 原始图像路径
    method_dict : dict, # 方法字典 (包含 SSIM 参数)
):
    """
    计算每个重建图像与原始图像之间的 SSIM disimilarity map (1-SSIM)，
    并使用乘积方式综合多波段 disimilarity map。

    Args:
        reconstruct_img_paths (list[str]): 重建图像的文件路径列表。
        origin_img_path (str): 原始图像的文件路径。
        method_dict (dict): 指定 SSIM 方法和参数的字典。

    Returns:
        list[np.ndarray]: 一个 NumPy 数组列表，每个数组代表一个重建图像的
                         多波段综合 disimilarity map (1-SSIM)。
    """
    print(f"\n正在计算 disimilarity map (1-SSIM) for original image: {origin_img_path}")
    print(f"找到 {len(reconstruct_img_paths)} 个重建图像。")

    disim_tiff_list = []
    win_size = method_dict.get("ssim_win_size", 11)
    data_range = method_dict.get("ssim_data_range", 1) # 默认 1

    print(f"SSIM 窗口大小: {win_size}, 数据范围: {data_range}")

    try:
        with rasterio.open(origin_img_path) as src_orig:
            # 读取原始图像数据并转换为 NumPy 数组， skimage 期望 H, W, C 或 H, W
            origin_data = src_orig.read().astype(np.float32) # 使用 float32 避免类型问题
            # Transpose from C, H, W to H, W, C for skimage
            origin_data_hwc = np.transpose(origin_data, (1, 2, 0)) # 形状 [H, W, C]
            num_bands = origin_data_hwc.shape[-1]
            print(f"原始图像有 {num_bands} 个波段。")

            for recon_path in tqdm(reconstruct_img_paths, desc="计算 disimilarity map"):
                try:
                    with rasterio.open(recon_path) as src_recon:
                        recon_data = src_recon.read().astype(np.float32)
                        recon_data_hwc = np.transpose(recon_data, (1, 2, 0)) # 形状 [H, W, C]

                        if origin_data_hwc.shape != recon_data_hwc.shape:
                            print(f"警告: 原始图像 ({origin_data_hwc.shape}) 与重建图像 ({recon_data_hwc.shape}) {recon_path} 尺寸或波段数不匹配。跳过。")
                            continue

                        # 这里为了统一处理，先将数据归一化到 [0, 1]
                        origin_data_hwc = normalize_map(origin_data_hwc)
                        recon_data_hwc = normalize_map(recon_data_hwc)

                        # 计算结构相似性指数（SSIM），并获取全尺寸 disim map
                        # multichannel=True 会自动处理多波段，并返回每个波段的 SSIM map
                        # full=True 返回 SSIM map
                        # data_range 参数很重要，确保与图像数据范围匹配
                        try:
                             # Try with channel_axis for newer skimage versions
                             score, full_ssim_map = structural_similarity(
                                 origin_data_hwc, recon_data_hwc,
                                 channel_axis=2, full=True, win_size=win_size, data_range=data_range
                             )
                        except TypeError: # Fallback for older skimage versions without channel_axis
                            score, full_ssim_map = structural_similarity(
                                origin_data_hwc, recon_data_hwc,
                                multichannel=True, full=True, win_size=win_size, data_range=data_range
                            )

                        # 计算 disim map (1 - SSIM map)
                        full_disim_map = 1.0 - full_ssim_map # 形状 [H, W, C]

                        # 综合多波段 disim map: 对每个像素，将所有波段的 disim 值相乘
                        # np.prod 默认沿着 axis=0 相乘，这里需要沿着最后一个维度 (波段维度) 相乘
                        combined_disim_map = np.prod(full_disim_map, axis=-1) # 形状 [H, W]
                        # 6波段沿波段维度相加（避免噪声影响，增加鲁棒性）
                        # combined_disim_map = np.sum(full_disim_map, axis=-1) # 形状 [H, W]

                        disim_tiff_list.append(combined_disim_map)

                except rasterio.errors.RasterioIOError as e:
                    print(f"读取重建图像 {recon_path} 时出错: {e}")
                except Exception as e:
                    print(f"处理 {recon_path} 时发生错误: {e}")

    except rasterio.errors.RasterioIOError as e:
        print(f"读取原始图像 {origin_img_path} 时出错: {e}")
    except Exception as e:
        print(f"处理原始图像 {origin_img_path} 时发生错误: {e}")

    return disim_tiff_list


# 定义一个计算平均误差图的函数 (通用函数，用于平均 pixel_error 或 disim map)
def average_maps(
        map_list: list[np.ndarray], # 地图列表 (NumPy 数组)
):
    """
    计算地图列表的逐像素平均值。

    Args:
        map_list (list[np.ndarray]): 地图 (NumPy 数组) 列表。

    Returns:
        np.ndarray: 平均地图的 NumPy 数组，如果输入列表为空则返回 None。
    """
    if not map_list:
        print("没有地图可供平均。")
        return None

    # 将列表中 NumPy 数组沿第0维堆叠起来
    stacked_maps = np.stack(map_list, axis=0) # 形状 [C, H, W]
    # 沿着堆叠的维度计算平均值
    average_map = np.mean(stacked_maps, axis=0) # 形状 [H, W]

    return average_map

# 定义一个归一化函数 (Min-Max Scaling)
def normalize_map(
    input_map: np.ndarray # 输入的 NumPy 数组地图
):
    """
    对输入的 NumPy 数组地图进行 Min-Max 归一化，范围 [0, 1]。

    Args:
        input_map (np.ndarray): 输入的地图 NumPy 数组。

    Returns:
        np.ndarray: 归一化后的地图 NumPy 数组，范围 [0, 1]。
                    如果输入地图是常数，则返回全零数组。
    """
    min_val = np.min(input_map)
    max_val = np.max(input_map)

    if max_val == min_val:
        # 如果是常数地图，归一化为全零
        print("警告: 地图是常数，归一化为全零。")
        return np.zeros_like(input_map, dtype=np.float32)
    else:
        normalized_map = (input_map - min_val) / (max_val - min_val)
        return normalized_map.astype(np.float32) # 确保输出是 float32


# 定义一个遍历 tif 文件的程序
def FindTIFF(Folder):
    # 获取文件信息列表
    file_info = []
    # 遍历指定文件夹
    for root, dirs, files in os.walk(Folder):
        for file in files:
            if file.lower().endswith(('.tif', '.tiff')): # 过滤出 TIFF 文件
                file_path = os.path.join(root, file)
                file_info.append((file, file_path))
    # 返回文件列表及信息
    return file_info # file_info 是一个 [(文件名, 路径), ...] 的列表

# 定义一个遍历文件夹的程序
def FindFolders(Folder):
    # 获取文件夹信息列表
    folder_info = []
    # 遍历指定文件夹
    for root, dirs, files in os.walk(Folder):
        for dir_name in dirs:
            dir_path = os.path.join(root, dir_name)
            folder_info.append((dir_name, dir_path))
    # 返回文件夹列表及信息
    return folder_info  # folder_info 是一个 [(文件夹名, 路径), ...] 的列表


# 主程序
if __name__ == '__main__':
    # 获取文件路径
    reconstract_folder_info = FindFolders(Reconstract_DataFolder_dir) # 获取重建图像所在的各个区域文件夹信息

    # 遍历各个不同区域的 img 文件夹
    print("开始处理各个区域...")
    for place_name, place_folder_path in tqdm(reconstract_folder_info, desc="处理区域"):

        print(f"\n--- 正在处理区域: {place_name} ---")
        # 获取文件夹对应的原始图像路径
        origin_img_path = os.path.join(Origin_DataFolder_dir, f'{place_name}.tiff')

        if not os.path.exists(origin_img_path):
            print(f"警告: 在 '{Origin_DataFolder_dir}' 中未找到与区域 '{place_name}' 对应的原始图像 '{place_name}.tiff'。跳过。")
            continue

        # 查找该区域文件夹内的所有重建 TIFF 文件
        reconstructed_img_info = FindTIFF(place_folder_path)
        # 只选择文件名以 'predicted_' 开头的 TIFF 文件路径
        reconstructed_img_paths = [path for name, path in reconstructed_img_info if name.startswith("predicted_")]

        if not reconstructed_img_paths:
            print(f"在 '{place_folder_path}' 中未找到重建图像。跳过区域 '{place_name}'。")
            continue

        # 读取原始图像元数据，用于保存输出文件
        try:
            with rasterio.open(origin_img_path) as src_orig:
                 meta = src_orig.meta.copy() # 获取原始图像的元数据并复制

            # 更新输出文件的元数据模板
            meta.update({
                 'count': 1, # 输出是单波段图像
                 'dtype': 'float32', # 使用 float32 存储误差值
                 'nodata': None # 可以根据需要设置 nodata 值
             })

        except rasterio.errors.RasterioIOError as e:
             print(f"读取原始图像 {origin_img_path} 元数据时出错: {e}")
             continue
        except Exception as e:
             print(f"处理原始图像 {origin_img_path} 元数据时发生错误: {e}")
             continue


        # --- 1. 计算并处理像素误差图 (SE 或 MAE) ---
        pixel_error_tiff_list = tifflist_pixel_error(
            reconstruct_img_paths=reconstructed_img_paths,
            origin_img_path=origin_img_path,
            method_dict=METHOD_DICT
        )

        average_pixel_error_map = None
        if pixel_error_tiff_list:
            # 计算该区域内图像的平均像素误差图
            average_pixel_error_map = average_maps(pixel_error_tiff_list)

            if average_pixel_error_map is not None:
                # 暂时不归一化
                normalized_average_pixel_error_map = average_pixel_error_map

                # 归一化平均像素误差图
                # normalized_average_pixel_error_map = normalize_map(average_pixel_error_map)

                # 保存归一化后的平均像素误差图至 tiff 文件
                try:
                    output_filename_pixel_loss = f"{place_name}_AveragePixelLoss.tiff"
                    output_filepath_pixel_loss = os.path.join(AVPixelLoss_DataFolder_dir, output_filename_pixel_loss)

                    print(f"  正在保存平均像素误差图 ({METHOD_DICT['pixel_loss_type']}) 到: {output_filepath_pixel_loss}")
                    with rasterio.open(output_filepath_pixel_loss, 'w', **meta) as dst:
                         dst.write(normalized_average_pixel_error_map, 1)

                except rasterio.errors.RasterioIOError as e:
                     print(f"保存区域 '{place_name}' 的平均像素误差图时出错: {e}")
                except Exception as e:
                     print(f"保存区域 '{place_name}' 的平均像素误差图文件时发生错误: {e}")
            else:
                print(f"计算区域 '{place_name}' 的平均像素误差时失败。")
        else:
            print(f"区域 '{place_name}' 未生成有效的像素误差图。跳过平均计算和保存。")

        
        # --- 2. 计算并处理 disim map (1-SSIM) ---
        disim_tiff_list = tifflist_disim_map(
             reconstruct_img_paths=reconstructed_img_paths,
             origin_img_path=origin_img_path,
             method_dict=METHOD_DICT
        )

        average_disim_map = None
        if disim_tiff_list:
            # 计算该区域内图像的平均 disim map
            average_disim_map = average_maps(disim_tiff_list)

            if average_disim_map is not None:
                # 归一化平均 disim map
                normalized_average_disim_map = normalize_map(average_disim_map)

                # 保存归一化后的平均 disim map 至 tiff 文件
                try:
                    output_filename_disim = f"{place_name}_AverageDisim.tiff"
                    output_filepath_disim = os.path.join(AVDisim_DataFolder_dir, output_filename_disim)

                    print(f"  正在保存平均 disimilarity map 到: {output_filepath_disim}")
                    with rasterio.open(output_filepath_disim, 'w', **meta) as dst:
                         dst.write(normalized_average_disim_map, 1)

                except rasterio.errors.RasterioIOError as e:
                     print(f"保存区域 '{place_name}' 的平均 disimilarity map 时出错: {e}")
                except Exception as e:
                     print(f"保存区域 '{place_name}' 的平均 disimilarity map 文件时发生错误: {e}")
            else:
                print(f"计算区域 '{place_name}' 的平均 disimilarity map 时失败。")
        else:
             print(f"区域 '{place_name}' 未生成有效的 disimilarity map。跳过平均计算和保存。")

        # --- 3. 综合像素误差和 disim map 并保存 ---
        if average_pixel_error_map is not None and average_disim_map is not None:
            # 确保两个平均地图都成功生成且归一化
            # Note: The normalize_map function already returns float32.
            normalized_average_pixel_error_map = normalize_map(average_pixel_error_map) # Re-normalize in case it was skipped due to error handling above
            normalized_average_disim_map = normalize_map(average_disim_map) # Re-normalize

            # 相乘综合
            combined_map = normalized_average_pixel_error_map * normalized_average_disim_map

            # 归一化综合结果
            normalized_combined_map = normalize_map(combined_map)

            # 保存归一化后的综合地图至 tiff 文件 (使用原文件名格式)
            try:
                 output_filename_combined_loss = f"{place_name}_AverageLoss.tiff" # 使用原命名规则
                 output_filepath_combined_loss = os.path.join(AVCombinedLoss_DataFolder_dir, output_filename_combined_loss)

                 print(f"  正在保存综合误差图到: {output_filepath_combined_loss}")
                 with rasterio.open(output_filepath_combined_loss, 'w', **meta) as dst:
                      dst.write(normalized_combined_map, 1)

            except rasterio.errors.RasterioIOError as e:
                 print(f"保存区域 '{place_name}' 的综合误差图时出错: {e}")
            except Exception as e:
                 print(f"保存区域 '{place_name}' 的综合误差图文件时发生错误: {e}")
        else:
            print(f"区域 '{place_name}' 未能生成像素误差或 disim map，跳过综合计算和保存。")


    print('\n全部计算完毕')