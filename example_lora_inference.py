#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LoRA模型推理使用示例
这个脚本展示了如何使用 inference_lora_load.py 进行LoRA微调后模型的推理
"""

import os
# import logging

# 直接使用绝对导入
from Prithvi_EO2_300MTL.inference_lora_load import inference_with_lora_adapter, main

def example_1_simple_inference():
    """
    示例1: 使用便捷函数进行简单推理
    """
    print("=== 示例1: 简单LoRA推理 ===")
    
    # 配置参数
    lora_adapter_path = "./Prithvi_EO2_300MTL/trained_models/EuroSAT/best_lora_epoch_35_valloss_0.078116"  # LoRA适配器路径
    data_files = ["./Prithvi_EO2_300MTL/examples/Mexico_HLS.S30.T13REM.2018026T173609.v2.0_cropped.tif"]  # 输入图像文件列表
    config_path = "./Prithvi_EO2_300MTL/config.json"  # 配置文件路径
    base_checkpoint = "./Prithvi_EO2_300MTL/Prithvi_EO_V2_300M_TL.pt"  # 基础预训练模型权重
    output_dir = "./Prithvi_EO2_300MTL/output_lora_simple"  # 输出目录
    
    # 检查文件是否存在
    if not os.path.exists(lora_adapter_path):
        print(f"错误: LoRA适配器路径不存在: {lora_adapter_path}")
        return
    
    if not os.path.exists(data_files[0]):
        print(f"错误: 输入图像文件不存在: {data_files[0]}")
        return
    
    # 执行推理
    try:
        inference_with_lora_adapter(
            lora_adapter_path=lora_adapter_path,
            data_files=data_files,
            config_path=config_path,
            base_checkpoint=base_checkpoint,
            output_dir=output_dir,
            rgb_outputs=True,  # 输出RGB图像
            mask_ratio=0.75,   # 掩码比例
            # seed=42,            # 随机种子，确保结果可重复
            # input_indices = None, # 默认使用所有波段
        )
        print(f"推理完成！结果保存在: {output_dir}")
    except Exception as e:
        print(f"推理过程中出现错误: {e}")

def example_2_advanced_inference():
    """
    示例2: 使用主函数进行高级推理（更多控制选项）
    """
    print("\n=== 示例2: 高级LoRA推理 ===")
    
    # 配置参数
    data_files = ["./Prithvi_EO2_300MTL/examples/Mexico_HLS.S30.T13REM.2018026T173609.v2.0_cropped.tif"]  # 输入图像文件列表
    config_path = "./Prithvi_EO2_300MTL/config.json"  # 配置文件路径
    base_checkpoint = "./Prithvi_EO2_300MTL/Prithvi_EO_V2_300M_TL.pt"  # 基础预训练模型权重
    lora_adapter_path = "./Prithvi_EO2_300MTL/trained_models/EuroSAT/best_lora_epoch_35_valloss_0.078116"  # LoRA适配器路径
    output_dir = "./Prithvi_EO2_300MTL/output_lora_advanced"  # 输出目录
    
    # 输出文件路径字典（可选，用于自定义输出文件名）
    output_dict = {
        "OriRGB_dir": os.path.join(output_dir, "original_rgb.tiff"),
        "recon_img_dir": os.path.join(output_dir, "reconstructed_rgb.tiff"),
        "mask_img_dir": os.path.join(output_dir, "masked_rgb.tiff")
    }
    
    # 检查文件是否存在
    if not os.path.exists(lora_adapter_path):
        print(f"错误: LoRA适配器路径不存在: {lora_adapter_path}")
        return
    
    if not os.path.exists(data_files[0]):
        print(f"错误: 输入图像文件不存在: {data_files[0]}")
        return
    
    # 执行推理
    try:
        main(
            data_files=data_files,
            config_path=config_path,
            base_checkpoint=base_checkpoint,
            lora_adapter_path=lora_adapter_path,
            output_dir=output_dir,
            rgb_outputs=True,  # 输出RGB图像
            mask_ratio=0.75,   # 掩码比例
            input_indices=None,  # 使用所有波段
            output_dict=output_dict,  # 自定义输出文件路径
            # seed=42  # 随机种子
        )
        print(f"推理完成！结果保存在: {output_dir}")
    except Exception as e:
        print(f"推理过程中出现错误: {e}")

def example_3_batch_inference():
    """
    示例3: 批量推理多个LoRA模型
    """
    print("\n=== 示例3: 批量LoRA推理 ===")
    
    # 基础配置
    data_files = ["./examples/sample_image.tif"]
    config_path = "./config.json"
    base_checkpoint = "./Prithvi_EO_V2_300M_TL.pt"
    
    # 多个LoRA适配器路径（假设您有多个微调后的模型）
    lora_models = [
        {
            "name": "EuroSAT_model",
            "path": "./Prithvi_EO2_300MTL/trained_models/EuroSAT/best_lora_epoch_35_valloss_0.078116",
            "output_dir": "./Prithvi_EO2_300MTL/output_lora_eurosat"
        },
        # 可以添加更多模型
        # {
        #     "name": "Other_model",
        #     "path": "./Prithvi_EO2_300MTL/trained_models/other_lora_model",
        #     "output_dir": "./Prithvi_EO2_300MTL/output_lora_other"
        # }
    ]
    
    # 检查输入文件
    if not os.path.exists(data_files[0]):
        print(f"错误: 输入图像文件不存在: {data_files[0]}")
        return
    
    # 批量推理
    for model_info in lora_models:
        print(f"\n正在处理模型: {model_info['name']}")
        
        if not os.path.exists(model_info['path']):
            print(f"警告: LoRA模型路径不存在，跳过: {model_info['path']}")
            continue
        
        try:
            inference_with_lora_adapter(
                lora_adapter_path=model_info['path'],
                data_files=data_files,
                config_path=config_path,
                base_checkpoint=base_checkpoint,
                output_dir=model_info['output_dir'],
                rgb_outputs=True,
                mask_ratio=0.75,
            #   seed=42
            )
            print(f"模型 {model_info['name']} 推理完成！结果保存在: {model_info['output_dir']}")
        except Exception as e:
            print(f"模型 {model_info['name']} 推理失败: {e}")

def main_examples():
    """
    运行所有示例
    """
    print("LoRA模型推理示例脚本")
    print("=" * 50)
    
    # 运行示例1
    example_1_simple_inference()
    
    # 运行示例2
    # example_2_advanced_inference()
    
    # 运行示例3
    # example_3_batch_inference()
    
    print("\n所有示例运行完成！")

if __name__ == "__main__":
    main_examples()
