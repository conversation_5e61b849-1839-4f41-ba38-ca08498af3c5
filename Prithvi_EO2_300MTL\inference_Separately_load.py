import argparse  # 处理命令行参数
import functools  # 提供高阶函数工具，如 `partial` 和 `lru_cache`
import os  # 处理操作系统相关功能，如文件路径操作
from typing import List, Union  # 类型提示，用于提高代码可读性
import re  # 正则表达式处理文本数据
import datetime  # 处理时间和日期
import numpy as np  # 处理数值计算，如数组操作
import pandas as pd  # 处理表格数据，通常用于CSV文件或数据分析
import rasterio  # 处理遥感影像（栅格数据）
import torch  # PyTorch 深度学习库，用于运行模型推理
import yaml  # 解析 YAML 配置文件
from einops import rearrange  # 用于张量重塑和维度变换
import json
from functools import partial  # 方便创建带默认参数的函数
# from prithvi_mae_forold import PrithviMAE  # 旧版导入 PrithviMAE 预训练模型方法
# 导入本地文件及方法
from .models import PrithviMAE # 新版方法
from .utils.count_mean_std import calculate_band_statistics # 导入均值标准差计算方法替代config计算

NO_DATA = -9999  # 遥感数据中常见的无效值
NO_DATA_FLOAT = 0.0001  # 可能是浮点型的无效值
OFFSET = 0  # 偏移值，可能用于数据归一化
PERCENTILE = 99  # 可能用于计算数据的分位数（比如亮度归一化时去掉极端值）

# 处理 *orig_img* 和 *new_img* 以进行 RGB 可视化
def process_channel_group(orig_img, new_img, channels, mean, std):
    """处理 *orig_img* 和 *new_img* 以进行 RGB 可视化。
    每个波段使用 *data_mean* 和 *data_std* 缩放回原始范围，
    然后去除最低和最高百分位数以增强对比度。
    数据缩放到 (0, 1) 范围并堆叠 channels_first。

    Args:
        orig_img: torch.Tensor 表示原始图像（参考图像），形状 = (bands, H, W)。
        new_img: torch.Tensor 表示图像，形状 = (bands, H, W)。
        channels: list 列表，表示 RGB 通道的索引。
        mean: list 列表，表示每个波段的均值。
        std: list 列表，表示每个波段的标准差。

    Returns:
        torch.Tensor with shape (num_channels, height, width) 表示原始图像
        torch.Tensor with shape (num_channels, height, width) 表示另一个图像
    """

    mean = torch.tensor(np.asarray(mean)[:, None, None])  # C H W，将均值转换为 torch.Tensor，并扩展维度以匹配图像形状
    std = torch.tensor(np.asarray(std)[:, None, None]) # C H W，将标准差转换为 torch.Tensor，并扩展维度以匹配图像形状
    orig_img = orig_img[channels, ...] # 选择 RGB 通道
    valid_mask = torch.ones_like(orig_img, dtype=torch.bool)  # 创建一个与 orig_img 形状相同的布尔掩码，初始值为 True
    valid_mask[orig_img == NO_DATA_FLOAT] = False # 将无效值对应的掩码设置为 False


    # 恢复到原始数据范围（逆标准化）
    orig_img = (orig_img * std[channels]) + mean[channels] # 将原始图像缩放回原始数据范围
    new_img = (new_img[channels, ...] * std[channels]) + mean[channels] # 将处理后的图像缩放回原始数据范围（沿用原图的均值和标准差）

    # 重新缩放（增强对比度）
    max_value = max(3000, np.percentile(orig_img[valid_mask], PERCENTILE)) # 计算最大值，取 3000 和 PERCENTILE分位数的较大值
    # max_value = np.percentile(orig_img[valid_mask], PERCENTILE+(100-PERCENTILE)/2)  # 计算上分位数的值作为最大值
    min_value = OFFSET # 设置最小值
    # min_value = np.percentile(orig_img[valid_mask], (100-PERCENTILE)/2)  # 计算下分位数作为最小值

    orig_img = torch.clamp((orig_img - min_value) / (max_value - min_value), 0, 1) # 将原始图像缩放到 (0, 1) 范围
    new_img = torch.clamp((new_img - min_value) / (max_value - min_value), 0, 1) # 将处理后的图像缩放到 (0, 1) 范围

    # 将无效数据设置为零
    orig_img[~valid_mask] = 0 # 将无效值对应的像素设置为 0
    new_img[~valid_mask] = 0 # 将无效值对应的像素设置为 0

    return orig_img, new_img # 返回处理后的原始图像和处理后的图像

# 从 *file_path* 读取所有波段并返回图像+元数据信息
def read_geotiff(file_path: str):
    """从 *file_path* 读取所有波段并返回图像+元数据信息。

    Args:
        file_path: 图像文件的路径

    Returns:
        np.ndarray with shape (bands, height, width)
        形状为 (波段数, 高度, 宽度)的np.ndarray
        元数据信息字典
    """

    with rasterio.open(file_path) as src: # 使用 rasterio 打开 GeoTIFF 文件
        img = src.read() # 读取所有波段的图像数据
        meta = src.meta # 获取 GeoTIFF 文件的元数据
        try:
            coords = src.lnglat() # 尝试获取图像的经纬度坐标
        except:
            # 无法读取坐标
            coords = None # 如果读取失败，将坐标设为 None
    '''
    img数据格式如下，假如 img 的数组形状是 (2, 3, 4)，即2波段，每个波段3行4列
    img = [
    # 波段 1
    [
        [10, 20, 30, 40],  # 第一行
        [50, 60, 70, 80],  # 第二行
        [90, 100, 110, 120] # 第三行
    ],

    # 波段 2
    [
        [100, 200, 300, 400], # 第一行
        [500, 600, 700, 800], # 第二行
        [900, 1000, 1100, 1200] # 第三行
    ]
]
    '''
    return img, meta, coords # 返回图像数据、元数据和坐标

# 将多波段图像保存为 GeoTIFF 文件
def save_geotiff(image, output_path: str, meta: dict):
    """将多波段图像保存为 GeoTIFF 文件

    Args:
        image: np.ndarray with shape (bands, height, width)【形状为 (波段数, 高度, 宽度)的np.ndarray】
        output_path: 图像保存的路径
        meta: 包含元数据信息的字典.
    """

    with rasterio.open(output_path, "w", **meta) as dest: # 使用 rasterio 以写入模式打开 GeoTIFF 文件，并传入元数据
        for i in range(image.shape[0]): # 遍历每个波段
            dest.write(image[i, :, :], i + 1) # 将当前波段的图像数据写入 GeoTIFF 文件，波段索引从 1 开始

    return

# 将 torch.Tensor 类型的浮点图像转换为 np.uint8 类型的图像
def _convert_np_uint8(float_image: torch.Tensor):
    """将 torch.Tensor 类型的浮点图像转换为 np.uint8 类型的图像。

        参数:
            float_image: torch.Tensor 类型的浮点图像数据。

        返回:
            np.ndarray 类型的 uint8 图像数据。
        """
    image = float_image.numpy() * 255.0 # 将 torch.Tensor 转换为 numpy.ndarray，并将像素值缩放到 0-255 范围
    image = image.astype(dtype=np.uint8) # 将图像数据类型转换为 np.uint8

    return image # 返回转换后的图像数据

# 通过加载 *file_paths* 中的图像来构建输入示例
def load_example(
    file_paths: List[str],
    mean: List[float],
    std: List[float],
    indices: Union[list[int], None] = None,
):
    """通过加载 *file_paths* 中的图像来构建输入示例.

    Args:
        file_paths: 文件路径列表 .
        mean: 包含 *file_paths* 中每个图像的每个波段的均值的列表
        std: 包含 *file_paths* 中每个图像的每个波段的标准差的列表。
        indices: 可选参数，指定要使用的波段索引列表，如果为 None，则使用所有波段。

    Returns:
        包含创建的示例的 np.array
        *file_paths* 中每个图像的元信息列表
    """

    imgs = [] # 初始化图像数据列表
    metas = [] # 初始化元数据列表
    temporal_coords = [] # 初始化元数据列表
    location_coords = [] # 初始化地理位置坐标列表

    for file in file_paths: # 遍历文件路径列表
        img, meta, coords = read_geotiff(file) # 读取 GeoTIFF 文件

        # 重缩放（不对无效数据进行归一化）
        img = np.moveaxis(img, 0, -1)  # （更改数据排列顺序）将波段维度移动到最后一个维度，即将按波段存改为按像元存(计算机领域更常用)
        if indices is not None: # 如果指定了波段索引
            img = img[..., indices] # 保留前面所有维度，从最后一个维度中找到indices匹配的通道对应内容
        img = np.where(img == NO_DATA, NO_DATA_FLOAT, (img - mean) / std) # 将无效数据替换为 NO_DATA_FLOAT，并进行归一化

        imgs.append(img) # 将处理后的图像数据添加到图像数据列表
        metas.append(meta) # 将元数据添加到元数据列表
        if coords is not None: # 如果成功读取了地理位置坐标
            location_coords.append(coords) # 将地理位置坐标添加到列表

        try: # 尝试提取时间戳
            match = re.search(r'(\d{7,8}T\d{6})', file) # 使用正则表达式搜索时间戳（即找一个 7 位或 8 位数字，紧随其后的是一个 "T" 字符，随后是6位数字）
            if match: # 如果找到时间戳
                year = int(match.group(1)[:4]) # 提取年份
                julian_day = match.group(1).split('T')[0][4:] # 提取julian日
                if len(julian_day) == 3: # 如果julian日是3位数
                    julian_day = int(julian_day) # 直接转换为整数
                else:
                    julian_day = datetime.datetime.strptime(julian_day, '%m%d').timetuple().tm_yday # 将月日转换为julian日
                temporal_coords.append([year, julian_day]) # 将时间坐标添加到时间坐标列表
            else:
                continue

        except Exception as e: # 如果提取时间戳失败
            pass # 显式地什么都不做
            # print(f'Could not extract timestamp for {file} ({e})') # 打印错误信息

    # 将图像数据列表堆叠成一个 numpy 数组，形状为 (num_frames, H, W, C)
    # 即将多个文件同名像素点堆叠，形成一个像素点对应多个影像，每个影像的该像素点又对应其多个波段数据
    imgs = np.stack(imgs, axis=0)
    # 将波段维度移动到第一个维度，形状为 (C, num_frames, H, W)，并转换为 float32 类型
    # 即将其转化为分波段，每个波段内又有多个时序影像相叠
    imgs = np.moveaxis(imgs, -1, 0).astype("float32")
    # 添加批次维度，形状为 (1, C, num_frames, H, W)
    imgs = np.expand_dims(imgs, axis=0)

    return imgs, temporal_coords, location_coords, metas # 返回处理后的图像数据、时间坐标、地理位置坐标和元数据

# 使用输入的 *input_data* 运行 *model* 并从输出的tokens中创建图像（掩码mask、重建图像reconstructed+可见图像visible）
def run_model(
    model: torch.nn.Module,
    input_data: torch.Tensor,
    temporal_coords: None | torch.Tensor,
    location_coords: None | torch.Tensor,
    mask_ratio: float,
    device: torch.device,
    seed: int = None,
):
    """使用输入的 *input_data* 运行 *model* 并从输出的tokens中创建图像（掩码mask、重建图像reconstructed+可见图像visible）

    Args:
        model: 要运行的MAE模型
        input_data: 形状为(B, C, T, H, W)的输入的torch.Tensor数据
        mask_ratio: 要使用的掩码比例
        device: 模型运行的设备

    Returns:
        2个 torch.Tensor，形状都为 (B, C, T, H, W)，分别是重建图像和掩码图像。
    """

    with torch.no_grad(): # 关闭梯度计算，因为是推理阶段，不需要反向传播
        x = input_data.to(device) # 将输入数据移动到指定的设备上（此时的x传入的是长宽尺寸为224*224的大图切分窗口后的图）

        # 如果提供了随机种子，设置PyTorch的随机种子以确保结果可重复
        if seed is not None:
            torch.manual_seed(seed)
            if torch.cuda.is_available():
                torch.cuda.manual_seed_all(seed)

        # 执行运算，实际调用的是 model.forward()（这里就是指导入model实例的.forward函数）
        # 输入的x的形状为B, C, T, H, W
        _, pred, mask = model(x, temporal_coords, location_coords, mask_ratio)
        # 返回的mask的形状是 (B, N)，pred的形状是(B, N, P)，其中 B 是批次大小，N 是 patch 的数量，P 是每个 patch 展开出的所有像素数（包括时间、通道、patch面维度）

    # 创建掩码和预测图像 (un-patchify)
    mask_img = (
        # 将掩码从patch形式转换回图像形式，并移动到CPU上
        # model.unpatchify是将一个个小patches拼接成窗口224*224大小
        # mask.unsqueeze(-1)表示为在mask最后一维后再加一维用来与pred维度对齐
        # repeat(1, 1, pred.shape[-1])表示前两维度不变，新添加的维度重复P次（P 是每个 patch 展开出的所有像素点数量）
                        # ps：我感觉第m维度重复n次，其实就是重复第m+1层中括号内存储的要素重复n次。
                        # 比如一个列表[[a],[b],[c]]，第0维重复n次就是第1层的[]内按照
                        # [a],[b],[c]的排列顺序有n组，每组间又是用,号分割。
        # .detach()用于断开当前张量的梯度追踪
        model.unpatchify(mask.unsqueeze(-1).repeat(1, 1, pred.shape[-1])).detach().cpu()
    ) # 此时只包含掩码信息，非掩码部分暂时是空的

    # 将预测的图像块从patch形式转换回图像形式，并移动到CPU上
    # 用model.unpatchify将pred图块拼接，断开梯度关联并导出至cpu
    pred_img = model.unpatchify(pred).detach().cpu()

    # 混合可见图像块和预测图像块（pred_img 中未被掩膜覆盖的区域的像素值应该是未被模型重建，而是的model.unpatchify简单填充的）
    rec_img = input_data.clone() # 创建输入图像的副本，用于存储重建图像
    rec_img[mask_img == 1] = pred_img[
        mask_img == 1
    ]  # 二进制掩码：0表示保留，1表示移除，将掩码为1的区域用预测的图像块替换

    # 交换掩码图像中的0和1，使得被掩盖的图像块在图中显示为更暗（更好的可视化效果）
    mask_img = (~(mask_img.to(torch.bool))).to(torch.float) # 将掩码图像转换为布尔类型，取反，再转换回浮点类型

    return rec_img, mask_img  # 返回重建图像和掩码图像

# 用于保存每个时间步的 GeoTIFF 图像（原始图像、重建图像、掩膜图像）
def save_rgb_imgs(
    input_img, rec_img, mask_img, channels, mean, std, output_dir, meta_data,
    oriRGB_tif_dir=None,
    recon_tif_dir=None,
    mask_tif_dir=None,
):
    """包装函数，用于保存每个时间步的 GeoTIFF 图像（原始图像、重建图像、掩膜图像）。

    Args:
        input_img: 输入的 torch.Tensor，形状为 (C, T, H, W).
        rec_img: 重建的 torch.Tensor，形状为 (C, T, H, W).
        mask_img: 掩膜的 torch.Tensor，形状为 (C, T, H, W).
        channels: 表示 RGB 通道索引的列表.
        mean: 每个波段均值的列表.
        std: 每个波段标准差的列表.
        output_dir: 保存输出的目录.
        meta_data: 包含 GeoTIFF 元信息的字典列表.
        
        oriRGB_tif_dir：原始影像img文件路径
        recon_tif_dir: 新构建的img文件路径.
        mask_tif_dir: 新构建的mask文件路径.
    """

    for t in range(input_img.shape[1]): # 遍历每个时间步
        rgb_orig, rgb_pred = process_channel_group( # 调用 process_channel_group 函数处理 RGB 图像
            orig_img=input_img[:, t, :, :], # 获取当前时间步的原始图像
            new_img=rec_img[:, t, :, :],  # 获取当前时间步的重建图像
            channels=channels, # RGB 通道索引
            mean=mean, # 均值
            std=std, # 标准差
        )

        rgb_mask = mask_img[channels, t, :, :] * rgb_orig # 将掩膜图像应用于原始图像，以显示掩膜区域

        # 创建文件保存路径
        if oriRGB_tif_dir == None:
            oriRGB_tif_dir = os.path.join(output_dir, f"original_rgb_t{t}.tiff")
            recon_tif_dir = os.path.join(output_dir, f"predicted_rgb_t{t}.tiff")
            mask_tif_dir = os.path.join(output_dir, f"masked_rgb_t{t}.tiff")
        
        # 保存图像
        save_geotiff( # 保存原始 RGB 图像
            image=_convert_np_uint8(rgb_orig), # 将图像转换为 uint8 格式
            output_path=oriRGB_tif_dir, # 设置输出路径
            meta=meta_data[t],  # 设置元数据
        )

        save_geotiff( # 保存重建的 RGB 图像
            image=_convert_np_uint8(rgb_pred),
            output_path=recon_tif_dir,
            meta=meta_data[t],
        )

        save_geotiff( # 保存掩膜的 RGB 图像
            image=_convert_np_uint8(rgb_mask),
            output_path=mask_tif_dir,
            meta=meta_data[t],
        )

# 用于保存每个时间步的 GeoTIFF 图像（重建图像、掩膜图像）
def save_imgs(rec_img, mask_img, mean, std, output_dir, meta_data,
                recon_tif_dir=None,
                mask_tif_dir=None,):
    """包装函数，用于保存每个时间步的 GeoTIFF 图像（重建图像、掩膜图像）。

    Args:
        rec_img: 重建的 torch.Tensor，形状为 (C, T, H, W)。
        mask_img: 掩膜的 torch.Tensor，形状为 (C, T, H, W)。
        mean: 每个波段均值的列表。
        std: 每个波段标准差的列表。
        output_dir: 保存输出的目录。
        meta_data: 包含 GeoTIFF 元信息的字典列表。
        recon_tif_dir: 新构建的img文件路径.
        mask_tif_dir: 新构建的mask文件路径.
    """

    mean = torch.tensor(np.asarray(mean)[:, None, None])  # C H W，将均值列表转换为 torch.Tensor，并扩展维度以匹配图像形状
    std = torch.tensor(np.asarray(std)[:, None, None]) # C H W，将标准差列表转换为 torch.Tensor，并扩展维度以匹配图像形状

    for t in range(rec_img.shape[1]): # 遍历每个时间步
        # 恢复到原始数据范围

        # 将重建图像从归一化后的范围恢复到原始数据范围，并转换为 float32 类型
        rec_img_t = ((rec_img[:, t, :, :] * std) + mean).to(torch.float32)
        # 将掩膜图像转换为 int16 类型
        mask_img_t = mask_img[:, t, :, :].to(torch.int16)

        # 创建文件保存路径
        if recon_tif_dir == None:
            recon_tif_dir = os.path.join(output_dir, f"predicted_t{t}.tiff")
            mask_tif_dir = os.path.join(output_dir, f"masked_t{t}.tiff")
        
        # 保存图像
        # meta_data[t]['count']=6 # 设置元数据中的波段数为 6（因为模型输入了6个波段）
        save_geotiff( # 保存重建图像
            image=rec_img_t, # 要保存的图像数据
            output_path=recon_tif_dir, # 设置输出路径，文件名包含时间步索引
            meta=meta_data[t], # 设置元数据，使用对应时间步的元数据
        )

        save_geotiff( # 保存掩膜图像
            image=mask_img_t,
            output_path=mask_tif_dir,
            meta=meta_data[t],
        )

# 模型加载函数
def load_model(config,checkpoint):
    # 判断使用设备
    if torch.cuda.is_available():
        device = torch.device("cuda") # 如果 CUDA 可用，使用 GPU
    else:
        device = torch.device("cpu") # 否则使用 CPU

    model = PrithviMAE(**config) # 创建 PrithviMAE 模型
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad) # 计算模型参数量
    print(f"\n--> Model has {total_params:,} parameters.\n") # 打印模型参数量
    
    model.to(device) # 将模型移动到指定设备
    state_dict = torch.load(checkpoint, map_location=device) # 加载模型权重
    # 丢弃固定的位置嵌入权重
    for k in list(state_dict.keys()):
        if 'pos_embed' in k:
            del state_dict[k] # 删除位置嵌入权重
    model.load_state_dict(state_dict, strict=False) # 加载模型权重，允许不完全匹配
    print(f"Loaded checkpoint from {checkpoint}") # 打印加载成功的消息
    
    return model

# 传入模型进行推理的函数，和mian函数同架构，只是多了一个model传入接口
def Only_Inference(
    model,
    data_files: List[str],
    config_path: str,
    checkpoint: str,
    output_dir: str,
    rgb_outputs: bool,
    mask_ratio: float = None,
    input_indices: list[int] = None,
    OutPut_dict: dict[str] = None,
    seed: int = None,
    **kwargs,
):
    """
    只负责推理的函数，用加载好的模型、运行模型并保存结果。

    参数:
        data_files: 输入数据文件路径列表。
        config_path: 配置文件路径。
        checkpoint: 模型权重文件路径。
        output_dir: 输出结果目录。
        rgb_outputs: 是否输出 RGB 图像。
        mask_ratio: 可选参数，掩码比例。
        input_indices: 可选参数，输入波段索引列表。
        model: 已加载好权重的模型。
        **kewargs： 其他可能存在的参数
    """
    
    os.makedirs(output_dir, exist_ok=True) # 创建输出目录，如果已存在则不报错

    # 获取参数 --------
    # 用于手动获取参数 calculated_mean 和 calculated_std 将是浮点数列表
    # mean, std = calculate_band_statistics(
    #     file_paths=data_files, 
    #     indices=input_indices, 
    #     no_data_value=NO_DATA # 确保使用您定义的 NO_DATA 常量
    # )
    
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)['pretrained_cfg'] # 从配置文件加载预训练模型配置

    batch_size = 1 # 设置批次大小为 1
    bands = config['bands'] # 获取波段列表
    num_frames = len(data_files) # 获取时间步数（文件数量）
    mean = config['mean'] # 获取均值
    std = config['std'] # 获取标准差
    img_size = config['img_size'] # 获取图像块大小
    mask_ratio = mask_ratio or config['mask_ratio'] # 获取掩码比例，如果命令行参数未提供，则使用配置文件中的值

    if torch.cuda.is_available():
        device = torch.device("cuda") # 如果 CUDA 可用，使用 GPU
    else:
        device = torch.device("cpu") # 否则使用 CPU
    
    # 加载数据 ---------------------------------------------------------------------------------
    # 加载输入数据，包括图像数据、时间坐标、地理位置坐标和元数据
    input_data, temporal_coords, location_coords, meta_data = load_example(
        file_paths=data_files, indices=input_indices, mean=mean, std=std)
    
    model.eval() # 设置模型为评估模式
    channels = [bands.index(b) for b in ["B04", "B03", "B02"]]  # BGR -> RGB # 从bands列表中，获取 ["B04", "B03", "B02"] 波段的索引

    # 如果不能被 img_size 整除，则进行填充
    original_h, original_w = input_data.shape[-2:] # 获取原始图像的高度和宽度
    pad_h = img_size - (original_h % img_size) # 计算高度方向的填充量
    pad_w = img_size - (original_w % img_size) # 计算宽度方向的填充量
    input_data = np.pad(
        input_data, # 要进行填充的原始数组
        # 指定每个轴（维度）的填充数量(批次维, C, num_frames, H, W) 这里批次维、波段维、文件数量维均不填充，h、w后填充
        ((0, 0), (0, 0), (0, 0), (0, pad_h), (0, pad_w)),
        mode="reflect" # 填充模式为 'reflect'，表示填充的值来自于数组边缘的反射
    ) # 对输入数据进行填充

    # 构建边缘无重叠的滑动窗口input_data形状是(批次维度, C, num_frames, H, W)
    batch = torch.tensor(input_data, device="cpu") # 将输入数据转换为 torch.Tensor
    # 拆分原batch的第3、4维就是行列维 构建滑动窗口窗口大小和步长均为img_size
    # 构建后的windows形状为(batch_size, C, num_frames, num_windows_height, num_windows_width, img_size, img_size)
    windows = batch.unfold(3, img_size, img_size).unfold(4, img_size, img_size)
    h1, w1 = windows.shape[3:5] # 获取窗口在高度和宽度方向的数量（形状是(批次维度, C, num_frames, H, W)[3:5]就是第4-5个元素(右侧取不到)）
    windows = rearrange(
        windows, "b c t h1 w1 h w -> (b h1 w1) c t h w", h=img_size, w=img_size
        # b 批次维度、 c 通道维度、 t num_frames、 h1 行方向上窗口数、 w1 列方向上窗口数、 h 窗口的高度大小、 w 窗口的宽度大小
        # 合并后的形状为(batch_size * h1 * w1, C, num_frames, h, w)
    ) # 以窗口为关注对象重塑形状，相当于每个窗口内的是一组，每组中则是相同的存有c、t、h、w的形状的图

    # 如果窗口数量大于批次大小，则拆分为多个批次
    num_batches = windows.shape[0] // batch_size if windows.shape[0] > batch_size else 1 # 计算批次数量（即对于窗口数>1的num_batches=h1*w1）
    windows = torch.tensor_split(windows, num_batches, dim=0) # 将包含多个窗口的windows拆分为多批次
    # 即windows = [tensor1, tensor2, ..., tensor_{h1*w1}]，每个tensor形状为(batch_size, C, num_frames, h, w)

    temporal_coords = torch.Tensor(temporal_coords).unsqueeze(0).to(device) # 将时间坐标转换为 torch.Tensor 并移动到设备
    location_coords = torch.Tensor(location_coords[0]).unsqueeze(0).to(device) # 将地理位置坐标转换为 torch.Tensor 并移动到设备

    # 运行模型
    rec_imgs = [] # 初始化重建图像列表
    mask_imgs = [] # 初始化掩码图像列表
    # windows形状为(batch_size, C, num_frames, h, w)
    # 这里循环便相当于按窗口处理，每个x的形状仍为(batch_size, C, num_frames, h, w)即原作者注释的BCTHW
    for x in windows:
        rec_img, mask_img = run_model(model, x, temporal_coords, location_coords, mask_ratio, device, seed) # 运行模型(这里的x是224*224的窗口)
        rec_imgs.append(rec_img) # 将重建图像添加到列表
        mask_imgs.append(mask_img) # 将掩码图像添加到列表

    #  二者形状为(b h1 w1) c t h w
    rec_imgs = torch.concat(rec_imgs, dim=0) # 将重建图像列表连接成一个张量
    mask_imgs = torch.concat(mask_imgs, dim=0) # 将掩码图像列表连接成一个张量

    # 由图像块构建图像
    rec_imgs = rearrange(
        rec_imgs,
        "(b h1 w1) c t h w -> b c t (h1 h) (w1 w)", # 张量形状变形
        h=img_size, # 图像块的高度
        w=img_size, # 图像块的宽度
        b=1,
        c=len(bands),
        t=num_frames,
        h1=h1, # 高度方向的窗口数
        w1=w1, # 宽度方向的窗口数
    ) # 使用 einops 的 rearrange 函数将重建的图像块重新组合成完整的图像。

    mask_imgs = rearrange(
        mask_imgs,
        "(b h1 w1) c t h w -> b c t (h1 h) (w1 w)",
        h=img_size,
        w=img_size,
        b=1,
        c=len(bands),
        t=num_frames,
        h1=h1,
        w1=w1,
    ) # 使用 einops 的 rearrange 函数将掩码图像块重新组合成完整的图像。

    # 将填充的图像（图像形状为b c t (h1 h) (w1 w)，即(b c t H W)）裁剪回原始大小
    # :original_h 表示选择高度维度上的前 original_h 个像素
    rec_imgs_full = rec_imgs[..., :original_h, :original_w] # 将重建的图像裁剪回原始的高度和宽度
    mask_imgs_full = mask_imgs[..., :original_h, :original_w] # 将掩码图像裁剪回原始的高度和宽度
    batch_full = batch[..., :original_h, :original_w] # 将原始图像裁剪回原始的高度和宽度

    # 构建输出图像
    if rgb_outputs: # 如果 rgb_outputs 为 True，则输出 RGB 图像
        for d in meta_data:
            # 更新元数据，设置波段数为 3，数据类型为 uint8，压缩方式为 lzw，无效值为 0。
            d.update(count=3, dtype="uint8", compress="lzw", nodata=0)

        # 调用 save_rgb_imgs 函数保存 RGB 图像
        save_rgb_imgs(
            batch_full[0, ...], # 原始图像
            rec_imgs_full[0, ...], # 重建的图像
            mask_imgs_full[0, ...], # 掩码图像
            channels, # 掩码图像
            mean,
            std,
            output_dir, # 输出目录
            meta_data, # 元数据
            oriRGB_tif_dir = OutPut_dict.get("OriRGB_dir"),
            recon_tif_dir = OutPut_dict.get("recon_img_dir"),
            mask_tif_dir = OutPut_dict.get("mask_img_dir"),

        )

    # 如果 rgb_outputs 为 False，则输出原始波段的图像
    else:
        for d in meta_data:
            # 更新元数据，设置压缩方式为 lzw，无效值为 0。
            d.update(compress="lzw", nodata=0)

        save_imgs(
            rec_imgs_full[0, ...],
            mask_imgs_full[0, ...],
            mean,
            std,
            output_dir,
            meta_data,
            recon_tif_dir=OutPut_dict.get("recon_img_dir"),
            mask_tif_dir=OutPut_dict.get("mask_img_dir"),
        )

    # print("Done!") # 打印完成信息

# 原使用的主函数（现已被load_model + only_inference 取代）
def main(
    data_files: List[str],
    config_path: str,
    checkpoint: str,
    output_dir: str,
    rgb_outputs: bool,
    mask_ratio: float = None,
    input_indices: list[int] = None,
    OutPut_dict: dict[str] = None,
    **kwargs,
):
    """
    主函数，用于加载数据、运行模型并保存结果。

    参数:
        data_files: 输入数据文件路径列表。
        config_path: 配置文件路径。
        checkpoint: 模型权重文件路径。
        output_dir: 输出结果目录。
        rgb_outputs: 是否输出 RGB 图像。
        mask_ratio: 可选参数，掩码比例。
        input_indices: 可选参数，输入波段索引列表。
        model: 已加载好权重的模型。
        **kewargs： 其他可能存在的参数
    """
    os.makedirs(output_dir, exist_ok=True) # 创建输出目录，如果已存在则不报错

    # 获取参数 --------
    # 手动计算std和mean calculated_mean 和 calculated_std 将是浮点数列表
    mean, std = calculate_band_statistics(
        file_paths=data_files, 
        indices=input_indices, 
        no_data_value=NO_DATA # 确保使用您定义的 NO_DATA 常量
    )

    with open(config_path, "r") as f:
        config = yaml.safe_load(f)['pretrained_cfg'] # 从配置文件加载预训练模型配置

    batch_size = 1 # 设置批次大小为 1
    bands = config['bands'] # 获取波段列表
    num_frames = len(data_files) # 获取时间步数（文件数量）
    # mean = config['mean'] # 获取均值
    # std = config['std'] # 获取标准差
    coords_encoding = config['coords_encoding'] # 获取坐标编码配置
    img_size = config['img_size'] # 获取图像块大小
    mask_ratio = mask_ratio or config['mask_ratio'] # 获取掩码比例，如果命令行参数未提供，则使用配置文件中的值

    print(
        f"\nTreating {len(data_files)} files as {len(data_files)} time steps from the same location\n"
    ) # 打印处理的文件数量和时间步数信息
    if len(data_files) != 4:
        print(
            "The original model was trained for four time steps. \nResults with different numbers of time steps may vary"
        ) # 如果时间步数不等于 4，打印警告信息

    if torch.cuda.is_available():
        device = torch.device("cuda") # 如果 CUDA 可用，使用 GPU
    else:
        device = torch.device("cpu") # 否则使用 CPU

    print(f"Using {device} device.\n") # 打印使用的设备信息

    # 加载数据 ---------------------------------------------------------------------------------

    input_data, temporal_coords, location_coords, meta_data = load_example(
        file_paths=data_files, indices=input_indices, mean=mean, std=std
    )  # 加载输入数据，包括图像数据、时间坐标、地理位置坐标和元数据

    if len(temporal_coords) != num_frames and 'time' in coords_encoding:
        coords_encoding.pop('time') # 如果时间坐标数量与时间步数不匹配，并且配置中包含时间编码，则移除时间编码
    if not len(location_coords) and 'location' in coords_encoding:
        coords_encoding.pop('location') # 如果地理位置坐标为空，并且配置中包含位置编码，则移除位置编码

    # 创建模型并加载权重 -------------------------------------------------------------

    config.update(
        coords_encoding=coords_encoding,
        num_frames=num_frames,
        in_chans=len(bands),
    ) # 更新模型配置，包括坐标编码、时间步数和输入通道数

    model = PrithviMAE(**config) # 创建 PrithviMAE 模型

    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad) # 计算模型参数量
    # print(f"\n--> Model has {total_params:,} parameters.\n") # 打印模型参数量

    model.to(device) # 将模型移动到指定设备

    state_dict = torch.load(checkpoint, map_location=device) # 加载模型权重
    # 丢弃固定的位置嵌入权重
    for k in list(state_dict.keys()):
        if 'pos_embed' in k:
            del state_dict[k] # 删除位置嵌入权重
    model.load_state_dict(state_dict, strict=False) # 加载模型权重，允许不完全匹配
    # print(f"Loaded checkpoint from {checkpoint}") # 打印加载成功的消息

    # 运行模型 --------------------------------------------------------------------------------
    model.eval() # 设置模型为评估模式
    channels = [bands.index(b) for b in ["B04", "B03", "B02"]]  # BGR -> RGB # 从bands列表中，获取 ["B04", "B03", "B02"] 波段的索引

    # 如果不能被 img_size 整除，则进行填充
    original_h, original_w = input_data.shape[-2:] # 获取原始图像的高度和宽度
    pad_h = img_size - (original_h % img_size) # 计算高度方向的填充量
    pad_w = img_size - (original_w % img_size) # 计算宽度方向的填充量
    input_data = np.pad(
        input_data, # 要进行填充的原始数组
        # 指定每个轴（维度）的填充数量(批次维, C, num_frames, H, W) 这里批次维、波段维、文件数量维均不填充，h、w后填充
        ((0, 0), (0, 0), (0, 0), (0, pad_h), (0, pad_w)),
        mode="reflect" # 填充模式为 'reflect'，表示填充的值来自于数组边缘的反射
    ) # 对输入数据进行填充

    # 构建边缘无重叠的滑动窗口input_data形状是(批次维度, C, num_frames, H, W)
    batch = torch.tensor(input_data, device="cpu") # 将输入数据转换为 torch.Tensor
    # 拆分原batch的第3、4维就是行列维 构建滑动窗口窗口大小和步长均为img_size
    # 构建后的windows形状为(batch_size, C, num_frames, num_windows_height, num_windows_width, img_size, img_size)
    windows = batch.unfold(3, img_size, img_size).unfold(4, img_size, img_size)
    h1, w1 = windows.shape[3:5] # 获取窗口在高度和宽度方向的数量（形状是(批次维度, C, num_frames, H, W)[3:5]就是第4-5个元素(右侧取不到)）
    windows = rearrange(
        windows, "b c t h1 w1 h w -> (b h1 w1) c t h w", h=img_size, w=img_size
        # b 批次维度、 c 通道维度、 t num_frames、 h1 行方向上窗口数、 w1 列方向上窗口数、 h 窗口的高度大小、 w 窗口的宽度大小
        # 合并后的形状为(batch_size * h1 * w1, C, num_frames, h, w)
    ) # 以窗口为关注对象重塑形状，相当于每个窗口内的是一组，每组中则是相同的存有c、t、h、w的形状的图

    # 如果窗口数量大于批次大小，则拆分为多个批次
    num_batches = windows.shape[0] // batch_size if windows.shape[0] > batch_size else 1 # 计算批次数量（即对于窗口数>1的num_batches=h1*w1）
    windows = torch.tensor_split(windows, num_batches, dim=0) # 将包含多个窗口的windows拆分为多批次
    # 即windows = [tensor1, tensor2, ..., tensor_{h1*w1}]，每个tensor形状为(batch_size, C, num_frames, h, w)

    temporal_coords = torch.Tensor(temporal_coords).unsqueeze(0).to(device) # 将时间坐标转换为 torch.Tensor 并移动到设备
    location_coords = torch.Tensor(location_coords[0]).unsqueeze(0).to(device) # 将地理位置坐标转换为 torch.Tensor 并移动到设备

    # 运行模型
    rec_imgs = [] # 初始化重建图像列表
    mask_imgs = [] # 初始化掩码图像列表
    # windows形状为(batch_size, C, num_frames, h, w)
    # 这里循环便相当于按窗口处理，每个x的形状仍为(batch_size, C, num_frames, h, w)即原作者注释的BCTHW
    for x in windows:
        rec_img, mask_img = run_model(model, x, temporal_coords, location_coords, mask_ratio, device, seed) # 运行模型(这里的x是224*224的窗口)
        rec_imgs.append(rec_img) # 将重建图像添加到列表
        mask_imgs.append(mask_img) # 将掩码图像添加到列表

    #  二者形状为(b h1 w1) c t h w
    rec_imgs = torch.concat(rec_imgs, dim=0) # 将重建图像列表连接成一个张量
    mask_imgs = torch.concat(mask_imgs, dim=0) # 将掩码图像列表连接成一个张量

    # 由图像块构建图像
    rec_imgs = rearrange(
        rec_imgs,
        "(b h1 w1) c t h w -> b c t (h1 h) (w1 w)", # 张量形状变形
        h=img_size, # 图像块的高度
        w=img_size, # 图像块的宽度
        b=1,
        c=len(bands),
        t=num_frames,
        h1=h1, # 高度方向的窗口数
        w1=w1, # 宽度方向的窗口数
    ) # 使用 einops 的 rearrange 函数将重建的图像块重新组合成完整的图像。

    mask_imgs = rearrange(
        mask_imgs,
        "(b h1 w1) c t h w -> b c t (h1 h) (w1 w)",
        h=img_size,
        w=img_size,
        b=1,
        c=len(bands),
        t=num_frames,
        h1=h1,
        w1=w1,
    ) # 使用 einops 的 rearrange 函数将掩码图像块重新组合成完整的图像。

    # 将填充的图像（图像形状为b c t (h1 h) (w1 w)，即(b c t H W)）裁剪回原始大小
    # :original_h 表示选择高度维度上的前 original_h 个像素
    rec_imgs_full = rec_imgs[..., :original_h, :original_w] # 将重建的图像裁剪回原始的高度和宽度
    mask_imgs_full = mask_imgs[..., :original_h, :original_w] # 将掩码图像裁剪回原始的高度和宽度
    batch_full = batch[..., :original_h, :original_w] # 将原始图像裁剪回原始的高度和宽度

    # 构建输出图像
    if rgb_outputs: # 如果 rgb_outputs 为 True，则输出 RGB 图像
        for d in meta_data:
            # 更新元数据，设置波段数为 3，数据类型为 uint8，压缩方式为 lzw，无效值为 0。
            d.update(count=3, dtype="uint8", compress="lzw", nodata=0)

        # 调用 save_rgb_imgs 函数保存 RGB 图像
        save_rgb_imgs(
            batch_full[0, ...], # 原始图像
            rec_imgs_full[0, ...], # 重建的图像
            mask_imgs_full[0, ...], # 掩码图像
            channels, # 掩码图像
            mean,
            std,
            output_dir, # 输出目录
            meta_data, # 元数据
            oriRGB_tif_dir = OutPut_dict.get("OriRGB_dir"),
            recon_tif_dir = OutPut_dict.get("recon_img_dir"),
            mask_tif_dir = OutPut_dict.get("mask_img_dir"),

        )

    # 如果 rgb_outputs 为 False，则输出原始波段的图像
    else:
        for d in meta_data:
            # 更新元数据，设置压缩方式为 lzw，无效值为 0。
            d.update(compress="lzw", nodata=0)

        save_imgs(
            rec_imgs_full[0, ...],
            mask_imgs_full[0, ...],
            mean,
            std,
            output_dir,
            meta_data,
            recon_tif_dir=OutPut_dict.get("recon_img_dir"),
            mask_tif_dir=OutPut_dict.get("mask_img_dir"),
        )

    print("Done!") # 打印完成信息

# 文件直接被调用时的主进程（包括参数传递啥的）
if __name__ == "__main__":
    parser = argparse.ArgumentParser("MAE run inference", add_help=False)
    # 创建一个 ArgumentParser 对象，用于解析命令行参数。
    # "MAE run inference" 是程序的描述信息。
    # add_help=False 禁用默认的帮助信息，可以自定义帮助信息。

    parser.add_argument(
        "--data_files",
        type=str,
        nargs="+",
        default=["examples/Mexico_HLS.S30.T13REM.2018026T173609.v2.0_cropped.tif",
                 "examples/Mexico_HLS.S30.T13REM.2018106T172859.v2.0_cropped.tif",
                 "examples/Mexico_HLS.S30.T13REM.2018201T172901.v2.0_cropped.tif",
                 "examples/Mexico_HLS.S30.T13REM.2018266T173029.v2.0_cropped.tif",
                 ],
        help="Path to the data files. Assumes multi-band files.",
    )
    # 添加一个命令行参数 "--data_files"，用于指定输入数据文件的路径。
    # type=str 表示参数类型为字符串。
    # nargs="+" 表示参数可以接受一个或多个值，存储为一个列表。
    # default: 提供了默认的数据文件路径列表。
    # help: 提供了参数的帮助信息。

    parser.add_argument(
        "--config_path",
        "-c",
        type=str,
        default="config.json",
        help="Path to json file containing model training parameters.",
    )
    # 添加一个命令行参数 "--config_path" 或 "-c"，用于指定包含模型训练参数的 JSON 配置文件路径。
    # default: 提供了默认的配置文件路径。

    parser.add_argument(
        "--checkpoint",
        type=str,
        default="Prithvi_EO_V2_300M_TL.pt",
        help="Path to a checkpoint file to load from.",
    )
    # 添加一个命令行参数 "--checkpoint"，用于指定要加载的检查点文件路径。
    # default: 提供了默认的检查点文件路径。

    parser.add_argument(
        "--output_dir",
        type=str,
        default="output",
        help="Path to the directory where to save outputs.",
    )
    # 添加一个命令行参数 "--output_dir"，用于指定保存输出文件的目录路径。
    # default: 提供了默认的输出目录。

    parser.add_argument(
        "--mask_ratio",
        default=0.75,
        type=float,
        help="Masking ratio (percentage of removed patches). "
             "If None (default) use same value used for pretraining.",
    )
    # 添加一个命令行参数 "--mask_ratio"，用于指定掩码比例（删除的图像块的百分比）。
    # default: 提供了默认的掩码比例。

    parser.add_argument(
        "--input_indices",
        default=None,
        type=int,
        nargs="+",
        help="0-based indices of channels to be selected from the input. By default takes all.",
    )
    # 添加一个命令行参数 "--input_indices"，用于指定要从输入中选择的通道的 0-based 索引。
    # default: 如果未提供，则默认使用所有通道。
    # 若输入，则输入影像6个目标通道的index（从0开始），以空格为分隔。如输入0 1 2 3 4 5

    parser.add_argument(
        "--rgb_outputs",
        action="store_true",
        help="If present, output files will only contain RGB channels. "
             "Otherwise, all bands will be saved.",
    )
    # 添加一个命令行参数 "--rgb_outputs"，用于指定是否只输出 RGB 通道。
    # action="store_true" 表示如果命令行中出现该参数，则将其值设置为 True。

    args = parser.parse_args()
    # 解析命令行参数，并将结果存储在 args 对象中。

    main(**vars(args))
    # 调用 main 函数，并将解析后的命令行参数作为关键字参数传递给它。
    # vars(args) 将 args 对象转换为字典。
