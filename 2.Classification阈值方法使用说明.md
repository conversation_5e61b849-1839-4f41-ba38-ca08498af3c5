# 阈值方法使用说明

## 概述

`2.Classification_Manual_Threshold.py` 脚本现在支持三种可切换的阈值确定方法：

1. **手动阈值法 (manual)** - 使用用户指定的固定阈值
2. **分位数法 (percentile)** - 基于数据分布的分位数确定阈值
3. **肘点法 (elbow)** - 使用K-means聚类的肘点法自动确定阈值

## 使用方法

### 1. 分位数方法（默认）

```bash
# 使用默认95%分位数
python 2.Classification_Manual_Threshold.py

# 指定其他分位数（如80%）
python 2.Classification_Manual_Threshold.py --method percentile --percentile 80
```

**适用场景：**
- 数据分布相对稳定
- 需要控制异常检测的敏感度
- 快速处理大量数据

**参数说明：**
- `--percentile`: 分位数值（0-100），默认95

### 2. 手动阈值法

```bash
# 使用固定阈值0.5
python 2.Classification_Manual_Threshold.py --method manual --threshold 0.5

# 使用其他阈值
python 2.Classification_Manual_Threshold.py --method manual --threshold 0.3
```

**适用场景：**
- 已知最优阈值
- 需要在多个数据集间保持一致的阈值
- 对特定应用场景进行精确控制

**参数说明：**
- `--threshold`: 固定阈值值（必需参数）

### 3. 肘点法（新增）

```bash
# 使用默认参数
python 2.Classification_Manual_Threshold.py --method elbow

# 自定义参数
python 2.Classification_Manual_Threshold.py --method elbow --max_k 15 --sample_size 20000
```

**适用场景：**
- 数据分布未知或复杂
- 需要自适应阈值确定
- 数据中存在明显的聚类结构

**参数说明：**
- `--max_k`: 最大聚类数量，默认10
- `--sample_size`: 采样大小（用于加速计算），默认10000

## 方法比较

| 方法 | 优点 | 缺点 | 计算复杂度 |
|------|------|------|------------|
| 手动阈值 | 精确控制、快速 | 需要先验知识 | O(n) |
| 分位数法 | 简单、稳定 | 可能不适合复杂分布 | O(n log n) |
| 肘点法 | 自适应、无需先验知识 | 计算复杂、可能不稳定 | O(k×n×i) |

## 输出文件

所有方法都会生成相同格式的输出：
- 输入文件：`Average_Loss_tiff/*.tif`
- 输出文件：`MyModel_Mask_Result/*_Mask.tif`
- 文件格式：GeoTIFF，uint8类型，0表示正常，1表示异常

## 性能建议

1. **小数据集（< 1M像素）**：可以使用任何方法
2. **大数据集（> 10M像素）**：
   - 优先使用分位数法或手动阈值法
   - 肘点法建议减小`sample_size`参数
3. **实时处理**：推荐手动阈值法

## 参数调优建议

### 分位数法
- 高敏感度：使用较低分位数（85-90%）
- 低敏感度：使用较高分位数（95-99%）

### 肘点法
- 数据复杂度高：增加`max_k`（15-20）
- 计算资源有限：减少`sample_size`（5000-10000）
- 数据量大：增加`sample_size`（20000-50000）

## 故障排除

1. **肘点法运行缓慢**：减少`sample_size`或`max_k`参数
2. **异常检测过于敏感**：
   - 分位数法：提高分位数值
   - 手动阈值法：提高阈值
3. **异常检测不够敏感**：
   - 分位数法：降低分位数值
   - 手动阈值法：降低阈值

## 依赖包

确保安装以下Python包：
```bash
pip install numpy rasterio scikit-learn matplotlib scipy
```

## 注意事项

1. 肘点法使用随机采样，结果可能有轻微差异
2. 对于常数图像，所有方法都会生成全0掩码
3. 建议在正式使用前先用小样本测试不同方法的效果
