import argparse  # 处理命令行参数
import os  # 处理操作系统相关功能，如文件路径操作
from typing import List, Union  # 类型提示，用于提高代码可读性
import re  # 正则表达式处理文本数据
import datetime  # 处理时间和日期
import numpy as np  # 处理数值计算，如数组操作
import rasterio  # 处理遥感影像（栅格数据）
import torch  # PyTorch 深度学习库，用于运行模型推理
import yaml  # 解析 YAML 配置文件
from einops import rearrange  # 用于张量重塑和维度变换
import logging  # 日志记录
from peft import PeftModel  # 导入PEFT库用于加载LoRA权重

# 导入本地模型和工具
from .models import PrithviMAE  # 导入PrithviMAE模型
from .inference_Separately_load import process_channel_group # 处理 *orig_img* 和 *new_img* 以进行 RGB 可视化
from .inference_Separately_load import read_geotiff # 从 *file_path* 读取所有波段并返回图像+元数据信息
from .inference_Separately_load import save_geotiff # 将多波段图像保存为 GeoTIFF 文件
from .inference_Separately_load import _convert_np_uint8 # 将 torch.Tensor 类型的浮点图像转换为 np.uint8 类型的图像
from .inference_Separately_load import load_example # 通过加载 *file_paths* 中的图像来构建输入示例
from .inference_Separately_load import save_rgb_imgs # 用于保存每个时间步的 GeoTIFF 图像（原始图像、重建图像、掩膜图像）
from .inference_Separately_load import save_imgs # 用于保存每个时间步的 GeoTIFF 图像（重建图像、掩膜图像）
from .utils.count_mean_std import calculate_band_statistics # 导入均值标准差计算方法替代config计算

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

NO_DATA = -9999  # 遥感数据中常见的无效值
NO_DATA_FLOAT = 0.0001  # 可能是浮点型的无效值
OFFSET = 0  # 偏移值，可能用于数据归一化
PERCENTILE = 99  # 可能用于计算数据的分位数（比如亮度归一化时去掉极端值）

# 使用输入的 *input_data* 运行 *model* 并从输出的tokens中创建图像（掩码mask、重建图像reconstructed+可见图像visible）
def run_model(
    model: torch.nn.Module,
    input_data: torch.Tensor,
    temporal_coords: None | torch.Tensor,
    location_coords: None | torch.Tensor,
    mask_ratio: float,
    device: torch.device,
    seed: int = None,
):
    """使用输入的 *input_data* 运行 *model* 并从输出的tokens中创建图像（掩码mask、重建图像reconstructed+可见图像visible）

    Args:
        model: 要运行的MAE模型
        input_data: 形状为(B, C, T, H, W)的输入的torch.Tensor数据
        mask_ratio: 要使用的掩码比例
        device: 模型运行的设备

    Returns:
        2个 torch.Tensor，形状都为 (B, C, T, H, W)，分别是重建图像和掩码图像。
    """

    with torch.no_grad(): # 关闭梯度计算，因为是推理阶段，不需要反向传播
        x = input_data.to(device) # 将输入数据移动到指定的设备上（此时的x传入的是长宽尺寸为224*224的大图切分窗口后的图）

        # 如果提供了随机种子，设置PyTorch的随机种子以确保结果可重复
        if seed is not None:
            torch.manual_seed(seed)
            if torch.cuda.is_available():
                torch.cuda.manual_seed_all(seed)

        # 执行运算，实际调用的是 model.forward()（这里就是指导入model实例的.forward函数）
        # 输入的x的形状为B, C, T, H, W
        _, pred, mask = model(
            pixel_values=x.to(torch.float32),
            temporal_coords=temporal_coords,
            location_coords=location_coords,
            mask_ratio=mask_ratio
        )
        # 返回的mask的形状是 (B, N)，pred的形状是(B, N, P)，其中 B 是批次大小，N 是 patch 的数量，P 是每个 patch 展开出的所有像素数（包括时间、通道、patch面维度）

    # 创建掩码和预测图像 (un-patchify)
    mask_img = (
        # 将掩码从patch形式转换回图像形式，并移动到CPU上
        # model.unpatchify是将一个个小patches拼接成窗口224*224大小
        # mask.unsqueeze(-1)表示为在mask最后一维后再加一维用来与pred维度对齐
        # repeat(1, 1, pred.shape[-1])表示前两维度不变，新添加的维度重复P次（P 是每个 patch 展开出的所有像素点数量）
                        # ps：我感觉第m维度重复n次，其实就是重复第m+1层中括号内存储的要素重复n次。
                        # 比如一个列表[[a],[b],[c]]，第0维重复n次就是第1层的[]内按照
                        # [a],[b],[c]的排列顺序有n组，每组间又是用,号分割。
        # .detach()用于断开当前张量的梯度追踪
        model.unpatchify(mask.unsqueeze(-1).repeat(1, 1, pred.shape[-1])).detach().cpu()
    ) # 此时只包含掩码信息，非掩码部分暂时是空的

    # 将预测的图像块从patch形式转换回图像形式，并移动到CPU上
    # 用model.unpatchify将pred图块拼接，断开梯度关联并导出至cpu
    pred_img = model.unpatchify(pred).detach().cpu()

    # 混合可见图像块和预测图像块（pred_img 中未被掩膜覆盖的区域的像素值应该是未被模型重建，而是的model.unpatchify简单填充的）
    rec_img = input_data.clone() # 创建输入图像的副本，用于存储重建图像
    rec_img[mask_img == 1] = pred_img[
        mask_img == 1
    ]  # 二进制掩码：0表示保留，1表示移除，将掩码为1的区域用预测的图像块替换

    # 交换掩码图像中的0和1，使得被掩盖的图像块在图中显示为更暗（更好的可视化效果）
    mask_img = (~(mask_img.to(torch.bool))).to(torch.float) # 将掩码图像转换为布尔类型，取反，再转换回浮点类型

    return rec_img, mask_img  # 返回重建图像和掩码图像

# LoRA模型加载函数（基于外挂LoRA权重的方式）
def load_lora_model(config, base_checkpoint_path, lora_adapter_path):
    """
    加载带有LoRA适配器的模型

    Args:
        config: 配置文件
        base_checkpoint_path: 基础预训练模型权重路径
        lora_adapter_path: LoRA适配器权重文件夹路径

    Returns:
        加载完成的模型和设备信息
    """
    # 判断使用设备
    if torch.cuda.is_available():
        device = torch.device("cuda") # 如果 CUDA 可用，使用 GPU
    else:
        device = torch.device("cpu") # 否则使用 CPU

    logging.info(f'当前使用设备: {device}')

    # 1. 创建基础模型
    base_model = PrithviMAE(**config) # 创建 PrithviMAE 基础模型
    logging.info("基础模型 PrithviMAE 创建成功")

    # 2. 加载基础预训练权重
    logging.info(f"正在从 {base_checkpoint_path} 加载基础预训练权重...")
    state_dict = torch.load(base_checkpoint_path, map_location=device) # 加载模型权重

    # 丢弃固定的位置嵌入权重（如果存在尺寸不匹配）
    for k in list(state_dict.keys()):
        if 'pos_embed' in k:
            del state_dict[k] # 删除位置嵌入权重

    base_model.load_state_dict(state_dict, strict=False) # 加载模型权重，允许不完全匹配
    logging.info(f"基础预训练权重加载成功")

    # 3. 加载LoRA适配器
    logging.info(f"正在从 {lora_adapter_path} 加载LoRA适配器...")
    model = PeftModel.from_pretrained(base_model, lora_adapter_path) # 使用PEFT加载LoRA适配器
    logging.info("LoRA适配器加载成功")

    # 计算模型参数量
    total_params = sum(p.numel() for p in model.parameters()) # 计算总参数量
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad) # 计算可训练参数量
    logging.info(f"模型总参数量: {total_params:,}")
    logging.info(f"可训练参数量: {trainable_params:,}")

    model.to(device) # 将模型移动到指定设备

    return model

# 传入LoRA模型进行推理的函数
def lora_inference(
    model,
    data_files: List[str],
    config_path: str,
    output_dir: str,
    rgb_outputs: bool,
    mask_ratio: float = None,
    input_indices: list[int] = None,
    output_dict: dict[str] = None,
    seed: int = None,
    **kwargs,
):
    """
    使用LoRA微调后的模型进行推理

    Args:
        model: 已加载LoRA权重的模型
        data_files: 输入数据文件路径列表
        config_path: 模型配置文件路径
        output_dir: 输出结果目录
        rgb_outputs: 是否输出 RGB 图像
        mask_ratio: 可选参数，掩码比例
        input_indices: 可选参数，输入波段索引列表
        output_dict: 输出文件路径字典
        seed: 随机种子
        **kwargs: 其他可能存在的参数
    """
    # 判断使用设备
    if torch.cuda.is_available():
        device = torch.device("cuda") # 如果 CUDA 可用，使用 GPU
    else:
        device = torch.device("cpu") # 否则使用 CPU
    
    model.to(device) # 将模型移动到指定设备
    model.eval() # 设置为评估模式

    with open(config_path, "r") as f:
        config = yaml.safe_load(f)['pretrained_cfg'] # 从配置文件加载预训练模型配置

    os.makedirs(output_dir, exist_ok=True) # 创建输出目录，如果已存在则不报错
    logging.info(f"输出目录创建成功: {output_dir}")

    # 获取配置参数
    batch_size = 1 # 设置批次大小为 1
    bands = config['bands'] # 获取波段列表
    mean = config['mean'] # 获取均值
    std = config['std'] # 获取标准差
    img_size = config['img_size'] # 获取图像块大小
    mask_ratio = mask_ratio or config['mask_ratio'] # 获取掩码比例，如果命令行参数未提供，则使用配置文件中的值

    logging.info(f"处理 {len(data_files)} 个文件作为 {len(data_files)} 个时间步")
    if len(data_files) != 1:
        logging.warning("该模型针对单时序输入进行了微调，多时序输入的结果可能会有所不同")

    # 加载数据
    logging.info("开始加载输入数据...")
    input_data, temporal_coords, location_coords, meta_data = load_example(
        file_paths=data_files, indices=input_indices, mean=mean, std=std)
    logging.info("输入数据加载完成")

    # 设置RGB通道索引
    channels = [bands.index(b) for b in ["B04", "B03", "B02"]]  # BGR -> RGB # 从bands列表中，获取 ["B04", "B03", "B02"] 波段的索引

    # 如果不能被 img_size 整除，则进行填充
    original_h, original_w = input_data.shape[-2:] # 获取原始图像的高度和宽度
    pad_h = img_size - (original_h % img_size) # 计算高度方向的填充量
    pad_w = img_size - (original_w % img_size) # 计算宽度方向的填充量
    input_data = np.pad(
        input_data, # 要进行填充的原始数组
        # 指定每个轴（维度）的填充数量(批次维, C, num_frames, H, W) 这里批次维、波段维、文件数量维均不填充，h、w后填充
        ((0, 0), (0, 0), (0, 0), (0, pad_h), (0, pad_w)),
        mode="reflect" # 填充模式为 'reflect'，表示填充的值来自于数组边缘的反射
    ) # 对输入数据进行填充

    # 构建边缘无重叠的滑动窗口input_data形状是(批次维度, C, num_frames, H, W)
    batch = torch.tensor(input_data, device="cpu") # 将输入数据转换为 torch.Tensor
    # 拆分原batch的第3、4维就是行列维 构建滑动窗口窗口大小和步长均为img_size
    # 构建后的windows形状为(batch_size, C, num_frames, num_windows_height, num_windows_width, img_size, img_size)
    windows = batch.unfold(3, img_size, img_size).unfold(4, img_size, img_size)
    h1, w1 = windows.shape[3:5] # 获取窗口在高度和宽度方向的数量
    windows = rearrange(
        windows, "b c t h1 w1 h w -> (b h1 w1) c t h w", h=img_size, w=img_size
        # b 批次维度、 c 通道维度、 t num_frames、 h1 行方向上窗口数、 w1 列方向上窗口数、 h 窗口的高度大小、 w 窗口的宽度大小
        # 合并后的形状为(batch_size * h1 * w1, C, num_frames, h, w)
    ) # 以窗口为关注对象重塑形状，相当于每个窗口内的是一组，每组中则是相同的存有c、t、h、w的形状的图

    # 如果窗口数量大于批次大小，则拆分为多个批次
    num_batches = windows.shape[0] // batch_size if windows.shape[0] > batch_size else 1 # 计算批次数量
    windows = torch.tensor_split(windows, num_batches, dim=0) # 将包含多个窗口的windows拆分为多批次

    # 处理坐标信息
    temporal_coords = torch.Tensor(temporal_coords).unsqueeze(0).to(device) if temporal_coords else None # 将时间坐标转换为 torch.Tensor 并移动到设备
    location_coords = torch.Tensor(location_coords[0]).unsqueeze(0).to(device) if location_coords else None # 将地理位置坐标转换为 torch.Tensor 并移动到设备

    # 运行模型推理
    logging.info("开始模型推理...")
    rec_imgs = [] # 初始化重建图像列表
    mask_imgs = [] # 初始化掩码图像列表

    # 对每个窗口进行推理
    for i, x in enumerate(windows):
        # logging.info(f"处理窗口 {i+1}/{len(windows)}")
        rec_img, mask_img = run_model(model, x, temporal_coords, location_coords, mask_ratio, device, seed) # 运行模型
        rec_imgs.append(rec_img) # 将重建图像添加到列表
        mask_imgs.append(mask_img) # 将掩码图像添加到列表

    logging.info("模型推理完成，开始重建完整图像...")

    # 拼接所有窗口的结果
    rec_imgs = torch.concat(rec_imgs, dim=0) # 将重建图像列表连接成一个张量
    mask_imgs = torch.concat(mask_imgs, dim=0) # 将掩码图像列表连接成一个张量

    # 由图像块构建完整图像
    rec_imgs = rearrange(
        rec_imgs,
        "(b h1 w1) c t h w -> b c t (h1 h) (w1 w)", # 张量形状变形
        h=img_size, # 图像块的高度
        w=img_size, # 图像块的宽度
        b=1,
        c=len(bands),
        t=len(data_files),
        h1=h1, # 高度方向的窗口数
        w1=w1, # 宽度方向的窗口数
    ) # 使用 einops 的 rearrange 函数将重建的图像块重新组合成完整的图像。

    mask_imgs = rearrange(
        mask_imgs,
        "(b h1 w1) c t h w -> b c t (h1 h) (w1 w)",
        h=img_size,
        w=img_size,
        b=1,
        c=len(bands),
        t=len(data_files),
        h1=h1,
        w1=w1,
    ) # 使用 einops 的 rearrange 函数将掩码图像块重新组合成完整的图像。

    # 将填充的图像裁剪回原始大小
    rec_imgs_full = rec_imgs[..., :original_h, :original_w] # 将重建的图像裁剪回原始的高度和宽度
    mask_imgs_full = mask_imgs[..., :original_h, :original_w] # 将掩码图像裁剪回原始的高度和宽度
    batch_full = batch[..., :original_h, :original_w] # 将原始图像裁剪回原始的高度和宽度

    # 保存输出图像
    logging.info("开始保存输出图像...")
    if rgb_outputs: # 如果 rgb_outputs 为 True，则输出 RGB 图像
        for d in meta_data:
            # 更新元数据，设置波段数为 3，数据类型为 uint8，压缩方式为 lzw，无效值为 0。
            d.update(count=3, dtype="uint8", compress="lzw", nodata=0)

        # 调用 save_rgb_imgs 函数保存 RGB 图像
        save_rgb_imgs(
            batch_full[0, ...], # 原始图像
            rec_imgs_full[0, ...], # 重建的图像
            mask_imgs_full[0, ...], # 掩码图像
            channels, # RGB通道索引
            mean,
            std,
            output_dir, # 输出目录
            meta_data, # 元数据
            oriRGB_tif_dir = output_dict.get("OriRGB_dir") if output_dict else None,
            recon_tif_dir = output_dict.get("recon_img_dir") if output_dict else None,
            mask_tif_dir = output_dict.get("mask_img_dir") if output_dict else None,
        )
        logging.info("RGB图像保存完成")

    # 如果 rgb_outputs 为 False，则输出原始波段的图像
    else:
        for d in meta_data:
            # 更新元数据，设置压缩方式为 lzw，无效值为 0。
            d.update(compress="lzw", nodata=0)

        save_imgs(
            rec_imgs_full[0, ...],
            mask_imgs_full[0, ...],
            mean,
            std,
            output_dir,
            meta_data,
            recon_tif_dir=output_dict.get("recon_img_dir") if output_dict else None,
            mask_tif_dir=output_dict.get("mask_img_dir") if output_dict else None,
        )
        logging.info("多波段图像保存完成")

    logging.info("推理完成！")

# 主函数：加载LoRA模型并进行推理
def main(
    data_files: List[str],
    config_path: str,
    base_checkpoint: str,
    lora_adapter_path: str,
    output_dir: str,
    rgb_outputs: bool,
    mask_ratio: float = None,
    input_indices: list[int] = None,
    output_dict: dict[str] = None,
    seed: int = None,
    **kwargs,
):
    """
    主函数，用于加载LoRA模型、运行推理并保存结果。

    参数:
        data_files: 输入数据文件路径列表。
        config_path: 配置文件路径。
        base_checkpoint: 基础预训练模型权重文件路径。
        lora_adapter_path: LoRA适配器权重文件夹路径。
        output_dir: 输出结果目录。
        rgb_outputs: 是否输出 RGB 图像。
        mask_ratio: 可选参数，掩码比例。
        input_indices: 可选参数，输入波段索引列表。
        output_dict: 输出文件路径字典。
        seed: 随机种子。
        **kwargs: 其他可能存在的参数
    """

    logging.info("=== 开始LoRA模型推理 ===")

    # 获取参数
    # # 手动计算std和mean calculated_mean 和 calculated_std 将是浮点数列表
    # mean, std = calculate_band_statistics(
    #     file_paths=data_files, 
    #     indices=input_indices, 
    #     no_data_value=NO_DATA # 确保使用您定义的 NO_DATA 常量
    # )
    
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)['pretrained_cfg'] # 从配置文件加载预训练模型配置

    # 1. 加载LoRA模型
    logging.info("步骤1: 加载LoRA模型...")
    model = load_lora_model(config, base_checkpoint, lora_adapter_path)

    # 2. 执行推理
    logging.info("步骤2: 执行推理...")
    lora_inference(
        model=model,
        config_path=config_path,
        data_files=data_files,
        output_dir=output_dir,
        rgb_outputs=rgb_outputs,
        mask_ratio=mask_ratio,
        input_indices=input_indices,
        output_dict=output_dict,
        seed=seed,
        **kwargs
    )

    logging.info("=== LoRA模型推理完成 ===")

# 便捷函数：直接使用LoRA适配器路径进行推理
def inference_with_lora_adapter(
    lora_adapter_path: str,
    data_files: List[str],
    config_path: str = "./config.json",
    base_checkpoint: str = "./Prithvi_EO_V2_300M_TL.pt",
    output_dir: str = "./output_lora",
    rgb_outputs: bool = False,
    mask_ratio: float = None,
    input_indices: list[int] = None,
    seed: int = None,
):
    """
    便捷函数：使用LoRA适配器进行推理

    Args:
        lora_adapter_path: LoRA适配器权重文件夹路径
        data_files: 输入数据文件路径列表
        config_path: 配置文件路径，默认为 "./config.json"
        base_checkpoint: 基础预训练模型权重路径，默认为 "./Prithvi_EO_V2_300M_TL.pt"
        output_dir: 输出目录，默认为 "./output_lora"
        rgb_outputs: 是否输出RGB图像，默认为False
        mask_ratio: 掩码比例，默认使用配置文件中的值
        input_indices: 输入波段索引，默认使用所有波段
        seed: 随机种子，默认为None
    """

    main(
        data_files=data_files,
        config_path=config_path,
        base_checkpoint=base_checkpoint,
        lora_adapter_path=lora_adapter_path,
        output_dir=output_dir,
        rgb_outputs=rgb_outputs,
        mask_ratio=mask_ratio,
        input_indices=input_indices,
        seed=seed
    )

# 文件直接被调用时的主进程（包括参数传递等）
if __name__ == "__main__":
    parser = argparse.ArgumentParser("LoRA MAE 推理脚本", add_help=False)
    # 创建一个 ArgumentParser 对象，用于解析命令行参数。

    parser.add_argument(
        "--data_files",
        type=str,
        nargs="+",
        default=["examples/sample_image.tif"],
        help="输入数据文件路径。支持多波段文件。",
    )
    # 添加一个命令行参数 "--data_files"，用于指定输入数据文件的路径。

    parser.add_argument(
        "--config_path",
        "-c",
        type=str,
        default="config.json",
        help="包含模型训练参数的配置文件路径。",
    )
    # 添加一个命令行参数 "--config_path" 或 "-c"，用于指定配置文件路径。

    parser.add_argument(
        "--base_checkpoint",
        type=str,
        default="Prithvi_EO_V2_300M_TL.pt",
        help="基础预训练模型权重文件路径。",
    )
    # 添加一个命令行参数 "--base_checkpoint"，用于指定基础预训练模型权重文件路径。

    parser.add_argument(
        "--lora_adapter_path",
        type=str,
        required=True,
        help="LoRA适配器权重文件夹路径（必需参数）。",
    )
    # 添加一个命令行参数 "--lora_adapter_path"，用于指定LoRA适配器权重文件夹路径。

    parser.add_argument(
        "--output_dir",
        type=str,
        default="output_lora",
        help="保存输出文件的目录路径。",
    )
    # 添加一个命令行参数 "--output_dir"，用于指定保存输出文件的目录路径。

    parser.add_argument(
        "--mask_ratio",
        default=0.75,
        type=float,
        help="掩码比例（删除的图像块的百分比）。如果为None，则使用预训练时的相同值。",
    )
    # 添加一个命令行参数 "--mask_ratio"，用于指定掩码比例。

    parser.add_argument(
        "--input_indices",
        default=None,
        type=int,
        nargs="+",
        help="要从输入中选择的通道的0-based索引。默认情况下使用所有通道。",
    )
    # 添加一个命令行参数 "--input_indices"，用于指定要从输入中选择的通道索引。

    parser.add_argument(
        "--rgb_outputs",
        action="store_true",
        help="如果存在，输出文件将仅包含RGB通道。否则，将保存所有波段。",
    )
    # 添加一个命令行参数 "--rgb_outputs"，用于指定是否只输出 RGB 通道。

    parser.add_argument(
        "--seed",
        type=int,
        default=None,
        help="随机种子，用于确保结果可重复。",
    )
    # 添加一个命令行参数 "--seed"，用于指定随机种子。

    args = parser.parse_args()
    # 解析命令行参数，并将结果存储在 args 对象中。

    # 调用主函数
    main(**vars(args))
    # 调用 main 函数，并将解析后的命令行参数作为关键字参数传递给它。
