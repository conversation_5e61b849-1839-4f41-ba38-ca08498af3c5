import rasterio
from typing import List, Union  # 类型提示，用于提高代码可读性
import numpy as np
import os
from tqdm import tqdm

def read_geotiff(file_path: str):
    """从 *file_path* 读取所有波段并返回图像+元数据信息。

    Args:
        file_path: 图像文件的路径

    Returns:
        np.ndarray with shape (bands, height, width)
        形状为 (波段数, 高度, 宽度)的np.ndarray
        元数据信息字典
    """

    with rasterio.open(file_path) as src: # 使用 rasterio 打开 GeoTIFF 文件
        img = src.read() # 读取所有波段的图像数据
        meta = src.meta # 获取 GeoTIFF 文件的元数据
        try:
            coords = src.lnglat() # 尝试获取图像的经纬度坐标
        except:
            # 无法读取坐标
            coords = None # 如果读取失败，将坐标设为 None
    return img, meta, coords # 返回图像数据、元数据和坐标

def calculate_band_statistics(
    file_paths: List[str],
    indices: Union[list[int], None] = None,
    no_data_value: Union[int, float] = -9999 # 默认无效值
) -> tuple[List[float], List[float]]:
    """
    通过增量计算方式，计算给定文件路径中所有图像的指定波段的全局均值和标准差。
    将跳过 'no_data_value'。这种方法避免了一次性将所有像素数据加载到内存中。

    Args:
        file_paths: 图像文件路径列表。
        indices: 可选参数，指定要使用的波段索引列表，如果为 None，则使用所有波段。
        no_data_value: 图像中的无效数据值（例如 -9999 或 np.nan）。

    Returns:
        包含每个波段均值的浮点数列表。
        包含每个波段标准差的浮点数列表。
    """
    
    if not file_paths:
        raise ValueError("没有提供文件路径来计算统计量。")

    # 尝试读取第一个文件以确定波段数量，这决定了我们收集数据的列表大小
    try:
        first_img, _, _ = read_geotiff(file_paths[0])
        if indices is None:
            # 如果没有指定索引，则使用第一个图像的所有波段数量
            num_bands_to_process = first_img.shape[0]
        else:
            # 确保指定的索引不超出第一个图像的实际波段数量
            # 并根据有效索引的数量来确定要处理的波段数量
            num_bands_to_process = len([idx for idx in indices if idx < first_img.shape[0]])
            if num_bands_to_process == 0 and indices:
                raise ValueError("提供的波段索引均超出第一个图像的波段范围。")
    except Exception as e:
        raise RuntimeError(f"无法读取第一个图像以确定波段数量: {e}")

    # 初始化用于增量计算的列表：均值、M2（二阶矩）、计数
    means = [0.0] * num_bands_to_process
    M2s = [0.0] * num_bands_to_process
    counts = [0] * num_bands_to_process

    print("正在从所有文件中计算波段统计量...")
    for file_path in tqdm(file_paths):
        try:
            img, _, _ = read_geotiff(file_path) # img 此时形状为 (bands, height, width)
        except Exception as e:
            print(f"警告: 无法读取文件 {file_path} 进行统计量计算: {e}。跳过此文件。")
            continue

        # 选择指定波段
        img_selected_bands = img
        if indices is not None:
            # 确保选定的索引在图像的实际波段范围内，需按原始 indices 列表中的顺序获取数据
            temp_selected_bands = []
            current_file_band_indices = [] # 记录当前文件实际处理的波段索引
            for original_idx in indices:
                if original_idx < img.shape[0]:
                    temp_selected_bands.append(img[original_idx, :, :])
                    current_file_band_indices.append(original_idx)
                else:
                    print(f"警告: 文件 {file_path} 中索引 {original_idx} 超出波段范围 ({img.shape[0]} bands)。跳过此索引。")
            
            if not temp_selected_bands: # 如果所有指定索引都无效
                print(f"警告: 文件 {file_path} 中没有有效波段可供处理。跳过此文件。")
                continue

            img_selected_bands = np.array(temp_selected_bands)
        
        # 确保当前文件选择的波段数量与预期一致
        if img_selected_bands.shape[0] != num_bands_to_process:
            print(f"警告: 文件 {file_path} 的波段数量与预期不符。预期 {num_bands_to_process}，实际 {img_selected_bands.shape[0]}。跳过此文件。")
            continue

        # 遍历每个选定的波段，累积有效数据
        for i in range(num_bands_to_process):
            band_slice = img_selected_bands[i, :, :]
            
            # 根据 no_data_value 的类型（int/float 或 np.nan）过滤无效数据
            valid_mask = np.isfinite(band_slice)  # 同时去除 nan 和 inf
            if no_data_value is not None:
                valid_mask &= (band_slice != no_data_value)
            valid_data = band_slice[valid_mask]
            
            for val in valid_data:
                counts[i] += 1
                delta = val - means[i]
                means[i] += delta / counts[i]
                delta2 = val - means[i]
                M2s[i] += delta * delta2

    calculated_mean: List[float] = []
    calculated_std: List[float] = []
    
    for i in range(num_bands_to_process):
        current_count = counts[i]
        
        if current_count > 1:
            calculated_mean.append(float(means[i]))
            calculated_std.append(float(np.sqrt(M2s[i] / counts[i])))
        elif counts[i] == 1:
            calculated_mean.append(float(means[i]))
            calculated_std.append(0.0)  # 单个样本标准差为0
        else:
            print(f"警告: 波段 {i} 没有有效像素。")
            calculated_mean.append(0.0)
            calculated_std.append(1.0)

    return calculated_mean, calculated_std

# 查找.tif和tiff路径
def list_tif_files(directory):
    tif_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.lower().endswith(('.tif', '.tiff')):
                tif_files.append(os.path.join(root, file))
    return tif_files

if __name__ == "__main__":
    # 遍历data文件夹内的所有.tif或.tiff文件，返回一个列表
    # 获取目标文件夹所在目录
    current_file_path = os.path.abspath(__file__)
    current_directory = os.path.dirname(current_file_path)
    data_folder_path = os.path.join(current_directory ,'EuroSAT')
    
    # 获取文件路径 
    data_files = list_tif_files(data_folder_path)

    # 按数据情况初始化参数
    input_indices = [1, 2, 3, 8, 11, 12] # 输入波段
    NO_DATA = -9999  # 遥感数据中常见的无效值

    # 获取参数 --------
    # calculated_mean 和 calculated_std 将是浮点数列表
    mean, std = calculate_band_statistics(
        file_paths=data_files, 
        indices=input_indices, 
        no_data_value=NO_DATA # 确保使用您定义的 NO_DATA 常量
    )

    print('mean:',mean)
    print('std:',std)

