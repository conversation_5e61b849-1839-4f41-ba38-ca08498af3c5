
"""
坐标编码模块
Coordinate encoding modules for Prithvi model
"""

import torch
import torch.nn as nn
from ..utils.position_encoding import _get_1d_sincos_embed_from_grid_torch

# 将时间信息（年份和日期）编码成高维特征向量，然后将这些侵入与patch特征结合
class TemporalEncoder(nn.Module):
    '''
    将时间信息（年份和日期）编码成高维特征向量，然后将这些侵入与patch特征结合
    '''
    def __init__(self, embed_dim: int, trainable_scale: bool = False): # 初始化函数
        super().__init__() # 调用父类的初始化函数
        self.embed_dim = embed_dim
        self.year_embed_dim = embed_dim // 2  # 年份编码维度，占总维度的一半
        self.julian_day_embed_dim = embed_dim - self.year_embed_dim  # 一年中的日期编码维度，占剩余维度

        # 如果可训练，则使用较小的数值初始化缩放因子（避免在训练初期对嵌入造成过大的影响）
        if trainable_scale:
            self.scale = nn.Parameter(torch.full((1,), 0.1)) # 可训练参数
        else:
            self.register_buffer('scale', torch.ones(1)) # 不可训练的缓冲区

    def forward(self, temporal_coords: torch.Tensor, tokens_per_frame: int | None = None):
        """
        temporal_coords: 年份和一年中的日期信息，形状为(B, T, 2)。
        tokens_per_frame: 样本中每帧的token数量。如果提供，嵌入将在T维度上重复，最终形状为(B, T*tokens_per_frame, embed_dim)。
        """
        # (B, T) + (-1,) 拼接后的结果是 (B, T, -1)
        shape = temporal_coords.shape[:2] + (-1,)  # B, T, -1 （即保留BT维度不变，展平HW的维度）
        # --- 步骤 1、2: 编码年和日期信息 ---
        year = _get_1d_sincos_embed_from_grid_torch( # 提取所有BT维的年份信息，展平为一个一维张量 (B * T,)后再重塑
            self.year_embed_dim, temporal_coords[:, :, 0].flatten()).reshape(shape) # (B, T, self.year_embed_dim)
        julian_day = _get_1d_sincos_embed_from_grid_torch( # 提取所有BT维的日期信息
            self.julian_day_embed_dim, temporal_coords[:, :, 1].flatten()).reshape(shape)
        # --- 步骤 3: 拼接年份和日期嵌入，并应用缩放 ---
        embedding = self.scale * torch.cat([year, julian_day], dim=-1) # 拼接成 (B, T, year_dim + julian_day_dim)
        # --- 步骤 4: 根据 tokens_per_frame 复制嵌入 ---
        if tokens_per_frame is not None: # 用于确保同一个T下的所有patch都有相同对应的时间嵌入 如会将 (B, T, D) 变为
            embedding = torch.repeat_interleave(embedding, tokens_per_frame, dim=1)
            # (B, T, D) -> (B, T*tokens_per_frame, D)
        return embedding  # B, T*tokens_per_frame, embed_dim

# 位置编码
class LocationEncoder(nn.Module):
    def __init__(self, embed_dim: int, trainable_scale: bool = False):
        """
        初始化 LocationEncoder。
        Args:
            embed_dim (int):
                地理位置嵌入的最终维度。这个维度会分成两部分：一部分用于纬度，一部分用于经度。
            trainable_scale (bool):
                是否使用一个可学习的缩放因子来缩放最终的地理位置嵌入。
                若为 True，缩放因子将作为模型参数进行优化；若为 False，缩放因子固定为 1.0。
        """
        super().__init__()  # 调用父类的初始化函数
        self.embed_dim = embed_dim
        self.lat_embed_dim = embed_dim // 2  # 纬度编码维度，占总维度的一半
        self.lon_embed_dim = embed_dim - self.lat_embed_dim  # 经度编码维度，占剩余维度

        # 如果可训练，则使用较小的数值初始化缩放因子（和时间编码处理思路一致）
        if trainable_scale:
            self.scale = nn.Parameter(torch.full((1,), 0.1))
        else:
            self.register_buffer('scale', torch.ones(1))

    def forward(self, location_coords: torch.Tensor):
        """
        location_coords: lat and lon info with shape，纬度和经度信息，形状为(B, 2).
        """
        # 保留location_coords的第0维，在(B,)形状基础上，进行元组加法。增维度1（长度为 1），并将剩余维度展平
        # (B,) + (1, -1) 拼接后的结果是 (B, 1, -1)
        shape = location_coords.shape[:1] + (1, -1)  # B, 1, -1
        # --- 步骤 1: 编码纬度、经度信息 ---
        lat = _get_1d_sincos_embed_from_grid_torch( # 提取所有批次的纬度信息，展平为一个一维张量 (B,)后再重塑
                self.lat_embed_dim, location_coords[:, 0].flatten()).reshape(shape)
        lon = _get_1d_sincos_embed_from_grid_torch(
                self.lon_embed_dim, location_coords[:, 1].flatten()).reshape(shape)
        # --- 步骤 3: 拼接纬度和经度嵌入，并应用缩放 ---
        embedding = self.scale * torch.cat([lat, lon], dim=-1)

        return embedding  # B, 1, embed_dim