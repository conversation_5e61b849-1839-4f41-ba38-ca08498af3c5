# 计算重建影像平均值的独立脚本
import os
import glob
import numpy as np
import rasterio
from tqdm import tqdm

def calculate_average_reconstruction(reconstruct_dir, average_output_dir):
    """
    计算每个原始影像的多次重建结果的平均值，并保存到指定目录
    
    参数:
        reconstruct_dir: 重建影像的根目录
        average_output_dir: 平均重建影像的输出目录
    """
    # 确保输出目录存在
    if not os.path.exists(average_output_dir):
        os.makedirs(average_output_dir)
    
    # 获取所有子文件夹（每个子文件夹对应一个原始影像）
    image_folders = [f.path for f in os.scandir(reconstruct_dir) if f.is_dir()]
    
    # 设置进度条
    progress_bar = tqdm(total=len(image_folders), desc="计算平均重建影像", unit="图像", ncols=100)
    
    # 处理每个影像文件夹
    for folder in image_folders:
        # 获取文件夹名称（不含路径）
        folder_name = os.path.basename(folder)
        
        # 查找该文件夹中所有重建影像文件
        recon_files = glob.glob(os.path.join(folder, "predicted_seed*.tiff"))
        
        if not recon_files:
            print(f"警告: 在 {folder} 中未找到重建影像文件")
            progress_bar.update(1)
            continue
        
        # 读取第一个文件以获取元数据和形状信息
        with rasterio.open(recon_files[0]) as src:
            profile = src.profile.copy()
            num_bands = src.count
            height = src.height
            width = src.width
            dtype = src.dtypes[0]
        
        # 创建一个数组来存储所有重建影像的数据
        all_data = np.zeros((len(recon_files), num_bands, height, width), dtype=np.float32)
        
        # 读取所有重建影像数据
        for i, file_path in enumerate(recon_files):
            with rasterio.open(file_path) as src:
                # 读取所有波段
                all_data[i] = src.read()
        
        # 计算每个波段的平均值
        avg_data = np.mean(all_data, axis=0).astype(dtype)
        
        # 构建输出文件路径
        output_file = os.path.join(average_output_dir, f"{folder_name}_avg.tiff")
        
        # 保存平均重建影像
        with rasterio.open(output_file, 'w', **profile) as dst:
            dst.write(avg_data)
        
        progress_bar.update(1)
    
    progress_bar.close()
    print(f"平均重建影像计算完成，结果已保存到 {average_output_dir}")


if __name__ == "__main__":
    
    '''
    
    执行语句
    python Self_Tools/calculate_average_reconstruction.py --input_dir Reconstract_Output --output_dir 1.AverageReconstract_Output
    
    '''
    
    import argparse
    
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="计算重建影像的平均值")
    parser.add_argument("--input_dir", type=str, required=True, help="重建影像的根目录")
    parser.add_argument("--output_dir", type=str, required=True, help="平均重建影像的输出目录")
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 执行平均重建影像计算
    calculate_average_reconstruction(args.input_dir, args.output_dir)
